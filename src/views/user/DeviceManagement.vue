<template>
  <div class="device-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>设备管理</span>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="用户ID">
          <el-input
            v-model="searchForm.user_id"
            placeholder="请输入用户ID"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="设备ID">
          <el-input
            v-model="searchForm.device_id"
            placeholder="请输入设备ID"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="设备名称">
          <el-input
            v-model="searchForm.device_name"
            placeholder="请输入设备名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="device_id" label="设备ID" min-width="200">
          <template #default="{ row }">
            <el-text class="device-id" copyable>{{ row.device_id }}</el-text>
          </template>
        </el-table-column>
        <el-table-column prop="device_name" label="设备名称" min-width="150">
          <template #default="{ row }">
            {{ row.device_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="alias" label="别名" min-width="120">
          <template #default="{ row }">
            {{ row.alias || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="user" label="绑定用户" width="120">
          <template #default="{ row }">
            {{ row.user?.username || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="platform" label="平台" width="100">
          <template #default="{ row }">
            {{ row.platform || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'info'">
              {{ row.is_active ? '活跃' : '非活跃' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="bound_at" label="绑定时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.bound_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="last_seen_at" label="最后活跃" width="180">
          <template #default="{ row }">
            {{ row.last_seen_at ? formatDateTime(row.last_seen_at) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="warning" size="small" @click="handleEditAlias(row)">编辑别名</el-button>
            <el-dropdown @command="(command) => handleAction(command, row)">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="unbind">解绑设备</el-dropdown-item>
                  <el-dropdown-item command="forceUnbind" divided>强制解绑</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchData"
          @current-change="fetchData"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="viewDialogVisible" title="设备详情" width="600px">
      <div v-if="currentDevice" class="device-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ currentDevice.id }}</el-descriptions-item>
          <el-descriptions-item label="设备ID">{{ currentDevice.device_id }}</el-descriptions-item>
          <el-descriptions-item label="设备名称">{{ currentDevice.device_name || '-' }}</el-descriptions-item>
          <el-descriptions-item label="别名">{{ currentDevice.alias || '-' }}</el-descriptions-item>
          <el-descriptions-item label="绑定用户">{{ currentDevice.user?.username || '-' }}</el-descriptions-item>
          <el-descriptions-item label="平台">{{ currentDevice.platform || '-' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentDevice.is_active ? 'success' : 'info'">
              {{ currentDevice.is_active ? '活跃' : '非活跃' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="绑定时间">{{ formatDateTime(currentDevice.bound_at) }}</el-descriptions-item>
          <el-descriptions-item label="最后活跃">
            {{ currentDevice.last_seen_at ? formatDateTime(currentDevice.last_seen_at) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="设备信息" :span="2">
            <pre>{{ JSON.stringify(currentDevice.device_info, null, 2) }}</pre>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="viewDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 编辑别名对话框 -->
    <el-dialog v-model="aliasDialogVisible" title="编辑设备别名" width="400px">
      <el-form :model="aliasForm" label-width="80px">
        <el-form-item label="设备别名">
          <el-input v-model="aliasForm.alias" placeholder="请输入设备别名" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="aliasDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAliasSubmit" :loading="aliasLoading">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ArrowDown } from '@element-plus/icons-vue'

  // 响应式数据
  const loading = ref(false)
  const tableData = ref<any[]>([])
  const selectedRows = ref<any[]>([])
  const viewDialogVisible = ref(false)
  const aliasDialogVisible = ref(false)
  const aliasLoading = ref(false)
  const currentDevice = ref<any>(null)

  // 搜索表单
  const searchForm = reactive({
    user_id: '',
    device_id: '',
    device_name: ''
  })

  // 别名表单
  const aliasForm = reactive({
    alias: ''
  })

  // 分页数据
  const pagination = reactive({
    page: 1,
    limit: 20,
    total: 0
  })

  // 获取数据 - 注意：设备管理接口在API文档中不存在
  const fetchData = async () => {
    loading.value = true
    try {
      // 由于API文档中没有设备管理相关接口，暂时返回空数据
      // 实际项目中应该调用相应的API接口
      tableData.value = []
      pagination.total = 0

      ElMessage.info('设备管理功能暂未开放，请联系管理员')
    } catch {
      ElMessage.error('获取数据失败')
    } finally {
      loading.value = false
    }
  }

  // 事件处理
  const handleSearch = () => {
    pagination.page = 1
    fetchData()
  }

  const handleReset = () => {
    Object.assign(searchForm, {
      user_id: '',
      device_id: '',
      device_name: ''
    })
    handleSearch()
  }

  const handleSelectionChange = (selection: any[]) => {
    selectedRows.value = selection
  }

  const handleView = (row: any) => {
    currentDevice.value = row
    viewDialogVisible.value = true
  }

  const handleEditAlias = (row: any) => {
    currentDevice.value = row
    aliasForm.alias = row.alias || ''
    aliasDialogVisible.value = true
  }

  const handleAction = async (command: string, row: any) => {
    switch (command) {
      case 'unbind':
        await handleUnbind(row, false)
        break
      case 'forceUnbind':
        await handleUnbind(row, true)
        break
    }
  }

  const handleUnbind = async (row: any, force: boolean) => {
    try {
      const message = force 
        ? '确定要强制解绑这个设备吗？此操作不会扣除用户时长。'
        : '确定要解绑这个设备吗？此操作可能会扣除用户时长。'
      
      await ElMessageBox.confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      // TODO: 调用解绑API
      ElMessage.success('解绑成功')
      fetchData()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('解绑失败')
      }
    }
  }

  const handleAliasSubmit = async () => {
    if (!currentDevice.value) return

    aliasLoading.value = true
    try {
      // TODO: 调用更新别名API
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 更新本地数据
      const index = tableData.value.findIndex(item => item.id === currentDevice.value.id)
      if (index !== -1) {
        tableData.value[index].alias = aliasForm.alias
      }
      
      ElMessage.success('别名更新成功')
      aliasDialogVisible.value = false
    } catch {
      ElMessage.error('别名更新失败')
    } finally {
      aliasLoading.value = false
    }
  }

  // 工具函数
  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 生命周期
  onMounted(() => {
    fetchData()
  })
</script>

<style lang="scss" scoped>
  .device-management {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .search-form {
      margin-bottom: 20px;
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }

    .device-detail {
      padding: 20px 0;

      pre {
        background-color: var(--el-fill-color-light);
        padding: 10px;
        border-radius: 4px;
        font-size: 12px;
        max-height: 200px;
        overflow-y: auto;
      }
    }

    .device-id {
      font-family: monospace;
      font-size: 12px;
    }
  }
</style>
