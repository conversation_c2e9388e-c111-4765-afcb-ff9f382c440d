<template>
  <div class="user-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增用户
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.search"
            placeholder="请输入用户名或邮箱"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="searchForm.role" placeholder="请选择角色" clearable>
            <el-option label="管理员" value="admin" />
            <el-option label="代理" value="reseller" />
            <el-option label="普通用户" value="user" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="活跃" value="active" />
            <el-option label="非活跃" value="inactive" />
            <el-option label="已封禁" value="banned" />
            <el-option label="待验证" value="pending_verification" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="avatar_url" label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :src="row.avatar_url" :size="40" :icon="UserFilled" />
          </template>
        </el-table-column>
        <el-table-column prop="username" label="用户名" min-width="120" />
        <el-table-column prop="nickname" label="昵称" min-width="120" />
        <el-table-column prop="email" label="邮箱" min-width="180" show-overflow-tooltip />
        <el-table-column prop="phone_number" label="手机号" width="130" />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="getRoleTagType(row.role)">
              {{ getRoleLabel(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="email_verified_at" label="邮箱验证" width="100">
          <template #default="{ row }">
            <el-tag :type="row.email_verified_at ? 'success' : 'warning'">
              {{ row.email_verified_at ? '已验证' : '未验证' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="注册时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-dropdown @command="(command) => handleAction(command, row)">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="ban" :disabled="row.status === 'banned'">
                    封禁用户
                  </el-dropdown-item>
                  <el-dropdown-item command="unban" :disabled="row.status !== 'banned'">
                    解封用户
                  </el-dropdown-item>
                  <el-dropdown-item command="resetPassword"> 重置密码 </el-dropdown-item>
                  <el-dropdown-item command="delete" divided> 删除用户 </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户详情对话框 -->
    <el-dialog v-model="dialogVisible" title="用户详情" width="800px" @close="handleDialogClose">
      <div v-if="currentUser" class="user-detail">
        <el-descriptions title="基本信息" :column="2" border>
          <el-descriptions-item label="ID">{{ currentUser.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ currentUser.username }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{
            currentUser.nickname || '-'
          }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ currentUser.email || '-' }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{
            currentUser.phone_number || '-'
          }}</el-descriptions-item>
          <el-descriptions-item label="角色">
            <el-tag :type="getRoleTagType(currentUser.role)">
              {{ getRoleLabel(currentUser.role) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(currentUser.status)">
              {{ getStatusLabel(currentUser.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="邮箱验证">
            <el-tag :type="currentUser.email_verified_at ? 'success' : 'warning'">
              {{ currentUser.email_verified_at ? '已验证' : '未验证' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="注册时间" :span="2">
            {{ formatDate(currentUser.created_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleEdit(currentUser)">编辑</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Plus, UserFilled, ArrowDown } from '@element-plus/icons-vue'
  import { AdminUserService } from '@/api/adminApi'
  import type { UserResponse } from '@/api/model/userModel'

  // 响应式数据
  const loading = ref(false)
  const tableData = ref<UserResponse[]>([])
  const selectedRows = ref<UserResponse[]>([])
  const dialogVisible = ref(false)
  const currentUser = ref<UserResponse | null>(null)

  // 搜索表单
  const searchForm = reactive({
    search: '',
    role: '',
    status: ''
  })

  // 分页数据
  const pagination = reactive({
    page: 1,
    limit: 20,
    total: 0
  })

  // 获取数据
  const fetchData = async () => {
    loading.value = true
    try {
      const params = {
        skip: (pagination.page - 1) * pagination.limit,
        limit: pagination.limit,
        ...searchForm
      }

      const response = await AdminUserService.getUsers(params)
      tableData.value = response.items
      pagination.total = response.total
    } catch {
      ElMessage.error('获取数据失败')
    } finally {
      loading.value = false
    }
  }

  // 事件处理
  const handleSearch = () => {
    pagination.page = 1
    fetchData()
  }

  const handleReset = () => {
    Object.assign(searchForm, {
      search: '',
      role: '',
      status: ''
    })
    handleSearch()
  }

  const handleAdd = () => {
    // 跳转到新增页面
    console.log('新增用户')
  }

  const handleView = (row: UserResponse) => {
    currentUser.value = row
    dialogVisible.value = true
  }

  const handleEdit = (row: UserResponse) => {
    // 跳转到编辑页面
    console.log('编辑用户', row)
  }

  const handleAction = async (command: string, row: UserResponse) => {
    switch (command) {
      case 'ban':
        await handleBanUser(row)
        break
      case 'unban':
        await handleUnbanUser(row)
        break
      case 'resetPassword':
        await handleResetPassword(row)
        break
      case 'delete':
        await handleDeleteUser(row)
        break
    }
  }

  const handleBanUser = async (row: UserResponse) => {
    try {
      await ElMessageBox.confirm('确定要封禁这个用户吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await AdminUserService.updateUser(row.id, { status: 'banned' })
      ElMessage.success('封禁成功')
      fetchData()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('封禁失败')
      }
    }
  }

  const handleUnbanUser = async (row: UserResponse) => {
    try {
      await ElMessageBox.confirm('确定要解封这个用户吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await AdminUserService.updateUser(row.id, { status: 'active' })
      ElMessage.success('解封成功')
      fetchData()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('解封失败')
      }
    }
  }

  const handleResetPassword = async () => {
    try {
      await ElMessageBox.confirm('确定要重置这个用户的密码吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      // 这里需要实现重置密码的API
      ElMessage.success('密码重置成功')
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('密码重置失败')
      }
    }
  }

  const handleDeleteUser = async (row: UserResponse) => {
    try {
      await ElMessageBox.confirm('确定要删除这个用户吗？此操作不可恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await AdminUserService.deleteUser(row.id)
      ElMessage.success('删除成功')
      fetchData()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  const handleSelectionChange = (selection: UserResponse[]) => {
    selectedRows.value = selection
  }

  const handleSizeChange = (size: number) => {
    pagination.limit = size
    pagination.page = 1
    fetchData()
  }

  const handleCurrentChange = (page: number) => {
    pagination.page = page
    fetchData()
  }

  const handleDialogClose = () => {
    currentUser.value = null
  }

  // 工具函数
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const getRoleLabel = (role: string) => {
    const labels: Record<string, string> = {
      admin: '管理员',
      reseller: '代理',
      user: '普通用户'
    }
    return labels[role] || role
  }

  const getRoleTagType = (role: string) => {
    const types: Record<string, string> = {
      admin: 'danger',
      reseller: 'warning',
      user: 'primary'
    }
    return types[role] || ''
  }

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      active: '活跃',
      inactive: '非活跃',
      banned: '已封禁',
      pending_verification: '待验证'
    }
    return labels[status] || status
  }

  const getStatusTagType = (status: string) => {
    const types: Record<string, string> = {
      active: 'success',
      inactive: 'info',
      banned: 'danger',
      pending_verification: 'warning'
    }
    return types[status] || ''
  }

  // 生命周期
  onMounted(() => {
    fetchData()
  })
</script>

<style scoped>
  .user-management {
    padding: 20px;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .search-form {
    margin-bottom: 20px;
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }

  .user-detail {
    margin-bottom: 20px;
  }

  .dialog-footer {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
  }
</style>
