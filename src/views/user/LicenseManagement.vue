<template>
  <div class="license-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>卡密管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增卡密
            </el-button>
            <el-button type="success" @click="handleBatchAdd">
              <el-icon><DocumentAdd /></el-icon>
              批量生成
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="卡密类型">
          <el-select v-model="searchForm.license_type_code" placeholder="请选择卡密类型" clearable>
            <el-option label="月卡" value="monthly" />
            <el-option label="季卡" value="quarterly" />
            <el-option label="年卡" value="yearly" />
            <el-option label="永久卡" value="lifetime" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="未激活" value="inactive" />
            <el-option label="已激活" value="active" />
            <el-option label="已过期" value="expired" />
            <el-option label="已禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item label="搜索">
          <el-input
            v-model="searchForm.search"
            placeholder="请输入卡密或用户名"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="key_string" label="卡密" min-width="200">
          <template #default="{ row }">
            <el-text class="license-key" copyable>{{ row.key_string }}</el-text>
          </template>
        </el-table-column>
        <el-table-column prop="license_type_name" label="类型" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="activated_user" label="激活用户" width="120">
          <template #default="{ row }">
            {{ row.activated_user?.username || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="activated_at" label="激活时间" width="180">
          <template #default="{ row }">
            {{ row.activated_at ? formatDateTime(row.activated_at) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="expires_at" label="过期时间" width="180">
          <template #default="{ row }">
            {{ row.expires_at ? formatDateTime(row.expires_at) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-dropdown @command="(command) => handleAction(command, row)">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="disable" :disabled="row.status === 'disabled'">
                    禁用
                  </el-dropdown-item>
                  <el-dropdown-item command="enable" :disabled="row.status !== 'disabled'">
                    启用
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchData"
          @current-change="fetchData"
        />
      </div>
    </el-card>

    <!-- 新增/编辑卡密对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑卡密' : '新增卡密'" width="500px">
      <el-form :model="licenseForm" :rules="licenseRules" ref="licenseFormRef" label-width="100px">
        <el-form-item label="卡密类型" prop="license_type_code">
          <el-select v-model="licenseForm.license_type_code" placeholder="请选择卡密类型" style="width: 100%">
            <el-option label="月卡" value="monthly" />
            <el-option label="季卡" value="quarterly" />
            <el-option label="年卡" value="yearly" />
            <el-option label="永久卡" value="lifetime" />
          </el-select>
        </el-form-item>
        <el-form-item label="有效期(天)" prop="duration_days" v-if="licenseForm.license_type_code !== 'lifetime'">
          <el-input-number v-model="licenseForm.duration_days" :min="1" :max="3650" style="width: 100%" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="licenseForm.remarks" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </template>
    </el-dialog>

    <!-- 批量生成对话框 -->
    <el-dialog v-model="batchDialogVisible" title="批量生成卡密" width="500px">
      <el-form :model="batchForm" :rules="batchRules" ref="batchFormRef" label-width="100px">
        <el-form-item label="卡密类型" prop="license_type_code">
          <el-select v-model="batchForm.license_type_code" placeholder="请选择卡密类型" style="width: 100%">
            <el-option label="月卡" value="monthly" />
            <el-option label="季卡" value="quarterly" />
            <el-option label="年卡" value="yearly" />
            <el-option label="永久卡" value="lifetime" />
          </el-select>
        </el-form-item>
        <el-form-item label="生成数量" prop="count">
          <el-input-number v-model="batchForm.count" :min="1" :max="1000" style="width: 100%" />
        </el-form-item>
        <el-form-item label="有效期(天)" prop="duration_days" v-if="batchForm.license_type_code !== 'lifetime'">
          <el-input-number v-model="batchForm.duration_days" :min="1" :max="3650" style="width: 100%" />
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input v-model="batchForm.remarks" type="textarea" :rows="3" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="batchDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleBatchSubmit" :loading="batchLoading">生成</el-button>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="viewDialogVisible" title="卡密详情" width="600px">
      <div v-if="currentLicense" class="license-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ currentLicense.id }}</el-descriptions-item>
          <el-descriptions-item label="卡密">{{ currentLicense.key_string }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ currentLicense.license_type_name }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(currentLicense.status)">
              {{ getStatusText(currentLicense.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="激活用户">{{ currentLicense.activated_user?.username || '-' }}</el-descriptions-item>
          <el-descriptions-item label="激活时间">
            {{ currentLicense.activated_at ? formatDateTime(currentLicense.activated_at) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="过期时间">
            {{ currentLicense.expires_at ? formatDateTime(currentLicense.expires_at) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(currentLicense.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentLicense.remarks || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="viewDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Plus, DocumentAdd, ArrowDown } from '@element-plus/icons-vue'
  import { AdminLicenseService } from '@/api/adminApi'

  // 响应式数据
  const loading = ref(false)
  const tableData = ref<any[]>([])
  const selectedRows = ref<any[]>([])
  const dialogVisible = ref(false)
  const batchDialogVisible = ref(false)
  const viewDialogVisible = ref(false)
  const isEdit = ref(false)
  const submitLoading = ref(false)
  const batchLoading = ref(false)
  const currentLicense = ref<any>(null)
  const licenseFormRef = ref()
  const batchFormRef = ref()

  // 搜索表单
  const searchForm = reactive({
    license_type_code: '',
    status: '',
    search: ''
  })

  // 卡密表单
  const licenseForm = reactive({
    license_type_code: '',
    duration_days: 30,
    remarks: ''
  })

  // 批量生成表单
  const batchForm = reactive({
    license_type_code: '',
    count: 10,
    duration_days: 30,
    remarks: ''
  })

  // 分页数据
  const pagination = reactive({
    page: 1,
    limit: 20,
    total: 0
  })

  // 表单验证规则
  const licenseRules = {
    license_type_code: [{ required: true, message: '请选择卡密类型', trigger: 'change' }]
  }

  const batchRules = {
    license_type_code: [{ required: true, message: '请选择卡密类型', trigger: 'change' }],
    count: [{ required: true, message: '请输入生成数量', trigger: 'blur' }]
  }

  // 获取数据
  const fetchData = async () => {
    loading.value = true
    try {
      const params = {
        skip: (pagination.page - 1) * pagination.limit,
        limit: pagination.limit,
        ...searchForm
      }

      const response = await AdminLicenseService.getLicenses(params)
      tableData.value = response.items
      pagination.total = response.total
    } catch {
      ElMessage.error('获取数据失败')
    } finally {
      loading.value = false
    }
  }

  // 事件处理
  const handleSearch = () => {
    pagination.page = 1
    fetchData()
  }

  const handleReset = () => {
    Object.assign(searchForm, {
      license_type_code: '',
      status: '',
      search: ''
    })
    handleSearch()
  }

  const handleSelectionChange = (selection: any[]) => {
    selectedRows.value = selection
  }

  const handleAdd = () => {
    isEdit.value = false
    Object.assign(licenseForm, {
      license_type_code: '',
      duration_days: 30,
      remarks: ''
    })
    dialogVisible.value = true
  }

  const handleBatchAdd = () => {
    Object.assign(batchForm, {
      license_type_code: '',
      count: 10,
      duration_days: 30,
      remarks: ''
    })
    batchDialogVisible.value = true
  }

  const handleView = (row: any) => {
    currentLicense.value = row
    viewDialogVisible.value = true
  }

  const handleEdit = (row: any) => {
    isEdit.value = true
    Object.assign(licenseForm, {
      id: row.id,
      license_type_code: row.license_type_code,
      duration_days: row.duration_days || 30,
      remarks: row.remarks || ''
    })
    dialogVisible.value = true
  }

  const handleAction = async (command: string, row: any) => {
    switch (command) {
      case 'disable':
        await handleUpdateStatus(row, 'disabled')
        break
      case 'enable':
        await handleUpdateStatus(row, 'active')
        break
      case 'delete':
        await handleDelete(row)
        break
    }
  }

  const handleUpdateStatus = async (row: any, status: string) => {
    try {
      await AdminLicenseService.updateLicense(row.id, { status })
      ElMessage.success('状态更新成功')
      fetchData()
    } catch {
      ElMessage.error('状态更新失败')
    }
  }

  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm('确定要删除这个卡密吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await AdminLicenseService.deleteLicense(row.id)
      ElMessage.success('删除成功')
      fetchData()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  const handleSubmit = async () => {
    if (!licenseFormRef.value) return

    await licenseFormRef.value.validate(async (valid: boolean) => {
      if (valid) {
        submitLoading.value = true
        try {
          if (isEdit.value) {
            await AdminLicenseService.updateLicense(licenseForm.id, licenseForm)
            ElMessage.success('更新成功')
          } else {
            await AdminLicenseService.createLicense(licenseForm)
            ElMessage.success('创建成功')
          }
          dialogVisible.value = false
          fetchData()
        } catch {
          ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
        } finally {
          submitLoading.value = false
        }
      }
    })
  }

  const handleBatchSubmit = async () => {
    if (!batchFormRef.value) return

    await batchFormRef.value.validate(async (valid: boolean) => {
      if (valid) {
        batchLoading.value = true
        try {
          await AdminLicenseService.batchCreateLicenses(batchForm)
          ElMessage.success('批量生成成功')
          batchDialogVisible.value = false
          fetchData()
        } catch {
          ElMessage.error('批量生成失败')
        } finally {
          batchLoading.value = false
        }
      }
    })
  }

  // 工具函数
  const getStatusTagType = (status: string) => {
    const statusMap: Record<string, string> = {
      inactive: 'info',
      active: 'success',
      expired: 'warning',
      disabled: 'danger'
    }
    return statusMap[status] || 'info'
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      inactive: '未激活',
      active: '已激活',
      expired: '已过期',
      disabled: '已禁用'
    }
    return statusMap[status] || status
  }

  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 生命周期
  onMounted(() => {
    fetchData()
  })
</script>

<style lang="scss" scoped>
  .license-management {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }

    .search-form {
      margin-bottom: 20px;
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }

    .license-detail {
      padding: 20px 0;
    }

    .license-key {
      font-family: monospace;
      font-size: 12px;
    }
  }
</style>
