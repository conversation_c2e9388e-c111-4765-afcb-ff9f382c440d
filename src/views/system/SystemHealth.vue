<template>
  <div class="system-health">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>系统健康状态</span>
          <el-button type="primary" @click="refreshHealth" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </template>

      <div v-loading="loading">
        <!-- 系统状态概览 -->
        <div class="status-overview">
          <div class="status-item">
            <div class="status-icon" :class="getStatusClass(healthData?.database_status)">
              <el-icon><DataBase /></el-icon>
            </div>
            <div class="status-info">
              <h4>数据库</h4>
              <p>{{ healthData?.database_status || '未知' }}</p>
            </div>
          </div>

          <div class="status-item">
            <div class="status-icon" :class="getStatusClass(healthData?.redis_status)">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="status-info">
              <h4>Redis缓存</h4>
              <p>{{ healthData?.redis_status || '未知' }}</p>
            </div>
          </div>

          <div class="status-item">
            <div class="status-icon" :class="getCpuStatusClass(healthData?.cpu_usage)">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="status-info">
              <h4>CPU使用率</h4>
              <p>{{ healthData?.cpu_usage ? `${healthData.cpu_usage.toFixed(1)}%` : '未知' }}</p>
            </div>
          </div>

          <div class="status-item">
            <div class="status-icon status-success">
              <el-icon><Timer /></el-icon>
            </div>
            <div class="status-info">
              <h4>运行时间</h4>
              <p>{{ healthData?.uptime || '未知' }}</p>
            </div>
          </div>
        </div>

        <!-- 详细信息 -->
        <div class="detail-info" v-if="healthData">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card class="info-card">
                <template #header>
                  <span>磁盘使用情况</span>
                </template>
                <div v-if="healthData.disk_usage">
                  <div v-for="(value, key) in healthData.disk_usage" :key="key" class="usage-item">
                    <span class="usage-label">{{ key }}:</span>
                    <span class="usage-value">{{ formatBytes(value) }}</span>
                  </div>
                </div>
              </el-card>
            </el-col>

            <el-col :span="12">
              <el-card class="info-card">
                <template #header>
                  <span>内存使用情况</span>
                </template>
                <div v-if="healthData.memory_usage">
                  <div v-for="(value, key) in healthData.memory_usage" :key="key" class="usage-item">
                    <span class="usage-label">{{ key }}:</span>
                    <span class="usage-value">{{ formatBytes(value) }}</span>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <el-row :gutter="20" style="margin-top: 20px;">
            <el-col :span="24">
              <el-card class="info-card">
                <template #header>
                  <span>系统信息</span>
                </template>
                <div class="system-info">
                  <div class="info-item">
                    <span class="info-label">系统版本:</span>
                    <span class="info-value">{{ healthData.version }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">最后检查时间:</span>
                    <span class="info-value">{{ formatTime(new Date()) }}</span>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, DataBase, Connection, Monitor, Timer } from '@element-plus/icons-vue'
import { AdminSystemService } from '@/api/adminApi'
import type { SystemHealthResponse } from '@/api/model/categoryModel'

const loading = ref(false)
const healthData = ref<SystemHealthResponse | null>(null)

// 获取系统健康状态
const fetchHealthData = async () => {
  try {
    loading.value = true
    const response = await AdminSystemService.getHealth()
    healthData.value = response.data
  } catch (error) {
    console.error('获取系统健康状态失败:', error)
    ElMessage.error('获取系统健康状态失败')
  } finally {
    loading.value = false
  }
}

// 刷新健康状态
const refreshHealth = () => {
  fetchHealthData()
}

// 获取状态样式类
const getStatusClass = (status?: string) => {
  if (!status) return 'status-unknown'
  
  switch (status.toLowerCase()) {
    case 'healthy':
    case 'connected':
    case 'ok':
      return 'status-success'
    case 'unhealthy':
    case 'disconnected':
    case 'error':
      return 'status-danger'
    default:
      return 'status-warning'
  }
}

// 获取CPU状态样式类
const getCpuStatusClass = (usage?: number) => {
  if (usage === undefined) return 'status-unknown'
  
  if (usage < 50) return 'status-success'
  if (usage < 80) return 'status-warning'
  return 'status-danger'
}

// 格式化字节数
const formatBytes = (bytes: any) => {
  if (typeof bytes !== 'number') return String(bytes)
  
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  if (bytes === 0) return '0 Bytes'
  
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// 格式化时间
const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN')
}

onMounted(() => {
  fetchHealthData()
})
</script>

<style lang="scss" scoped>
.system-health {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .status-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;

    .status-item {
      display: flex;
      align-items: center;
      padding: 20px;
      background: var(--el-bg-color-page);
      border-radius: 8px;
      border: 1px solid var(--el-border-color-light);

      .status-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 20px;

        &.status-success {
          background: var(--el-color-success-light-9);
          color: var(--el-color-success);
        }

        &.status-warning {
          background: var(--el-color-warning-light-9);
          color: var(--el-color-warning);
        }

        &.status-danger {
          background: var(--el-color-danger-light-9);
          color: var(--el-color-danger);
        }

        &.status-unknown {
          background: var(--el-color-info-light-9);
          color: var(--el-color-info);
        }
      }

      .status-info {
        h4 {
          margin: 0 0 5px 0;
          font-size: 16px;
          font-weight: 500;
        }

        p {
          margin: 0;
          color: var(--el-text-color-regular);
          font-size: 14px;
        }
      }
    }
  }

  .info-card {
    .usage-item, .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid var(--el-border-color-lighter);

      &:last-child {
        border-bottom: none;
      }

      .usage-label, .info-label {
        font-weight: 500;
        color: var(--el-text-color-regular);
      }

      .usage-value, .info-value {
        color: var(--el-text-color-primary);
      }
    }
  }
}
</style>
