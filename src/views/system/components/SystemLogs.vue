<template>
  <div class="system-logs">
    <!-- 搜索筛选 -->
    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="日志级别">
          <el-select
            v-model="searchForm.level"
            placeholder="请选择日志级别"
            clearable
            style="width: 150px"
          >
            <el-option label="DEBUG" value="debug" />
            <el-option label="INFO" value="info" />
            <el-option label="WARNING" value="warning" />
            <el-option label="ERROR" value="error" />
            <el-option label="CRITICAL" value="critical" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 系统日志表格 -->
    <el-table
      v-loading="loading"
      :data="systemLogs"
      stripe
      style="width: 100%"
      :row-class-name="getRowClassName"
    >
      <el-table-column prop="timestamp" label="时间" width="160">
        <template #default="{ row }">
          {{ formatTime(row.timestamp) }}
        </template>
      </el-table-column>
      <el-table-column prop="level" label="级别" width="100">
        <template #default="{ row }">
          <el-tag :type="getLevelTagType(row.level)">
            {{ row.level?.toUpperCase() }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="logger" label="记录器" width="150" show-overflow-tooltip />
      <el-table-column prop="module" label="模块" width="120" show-overflow-tooltip />
      <el-table-column prop="message" label="消息" min-width="300" show-overflow-tooltip />
      <el-table-column prop="user_id" label="用户ID" width="100" />
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="showLogDetail(row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="日志详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedLog" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="时间">
            {{ formatTime(selectedLog.timestamp) }}
          </el-descriptions-item>
          <el-descriptions-item label="级别">
            <el-tag :type="getLevelTagType(selectedLog.level)">
              {{ selectedLog.level?.toUpperCase() }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="记录器">
            {{ selectedLog.logger }}
          </el-descriptions-item>
          <el-descriptions-item label="模块">
            {{ selectedLog.module }}
          </el-descriptions-item>
          <el-descriptions-item label="用户ID">
            {{ selectedLog.user_id || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="请求ID">
            {{ selectedLog.request_id || '-' }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="log-message">
          <h4>消息内容</h4>
          <pre>{{ selectedLog.message }}</pre>
        </div>

        <div v-if="selectedLog.stack_trace" class="log-stack">
          <h4>堆栈跟踪</h4>
          <pre>{{ selectedLog.stack_trace }}</pre>
        </div>

        <div v-if="selectedLog.extra_data" class="log-extra">
          <h4>额外数据</h4>
          <pre>{{ JSON.stringify(selectedLog.extra_data, null, 2) }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { AdminLogService } from '@/api/adminApi'

const loading = ref(false)
const systemLogs = ref<any[]>([])
const detailDialogVisible = ref(false)
const selectedLog = ref<any>(null)

const searchForm = reactive({
  level: '',
  dateRange: [] as string[]
})

const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 获取系统日志列表
const fetchSystemLogs = async () => {
  try {
    loading.value = true
    
    const params: any = {
      skip: (pagination.page - 1) * pagination.limit,
      limit: pagination.limit
    }

    if (searchForm.level) {
      params.level = searchForm.level
    }
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.start_date = searchForm.dateRange[0]
      params.end_date = searchForm.dateRange[1]
    }

    const response = await AdminLogService.getSystemLogs(params)
    systemLogs.value = response.data.items
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取系统日志失败:', error)
    ElMessage.error('获取系统日志失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchSystemLogs()
}

// 重置
const handleReset = () => {
  searchForm.level = ''
  searchForm.dateRange = []
  pagination.page = 1
  fetchSystemLogs()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  fetchSystemLogs()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchSystemLogs()
}

// 显示日志详情
const showLogDetail = (log: any) => {
  selectedLog.value = log
  detailDialogVisible.value = true
}

// 获取级别标签类型
const getLevelTagType = (level: string) => {
  switch (level?.toLowerCase()) {
    case 'debug':
      return 'info'
    case 'info':
      return 'success'
    case 'warning':
      return 'warning'
    case 'error':
      return 'danger'
    case 'critical':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取行样式类名
const getRowClassName = ({ row }: { row: any }) => {
  const level = row.level?.toLowerCase()
  switch (level) {
    case 'error':
    case 'critical':
      return 'error-row'
    case 'warning':
      return 'warning-row'
    default:
      return ''
  }
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}

// 刷新数据
const refresh = () => {
  fetchSystemLogs()
}

// 暴露刷新方法给父组件
defineExpose({
  refresh
})

onMounted(() => {
  fetchSystemLogs()
})
</script>

<style lang="scss" scoped>
.system-logs {
  .search-form {
    margin-bottom: 20px;
    padding: 20px;
    background: var(--el-bg-color-page);
    border-radius: 8px;
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }

  .log-detail {
    .log-message,
    .log-stack,
    .log-extra {
      margin-top: 20px;

      h4 {
        margin-bottom: 10px;
        color: var(--el-text-color-primary);
      }

      pre {
        background: var(--el-bg-color-page);
        padding: 15px;
        border-radius: 4px;
        font-size: 12px;
        line-height: 1.5;
        max-height: 300px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
}

:deep(.el-table) {
  .error-row {
    background-color: var(--el-color-danger-light-9);
  }

  .warning-row {
    background-color: var(--el-color-warning-light-9);
  }
}
</style>
