<template>
  <div class="user-session-logs">
    <!-- 搜索筛选 -->
    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户ID">
          <el-input
            v-model="searchForm.user_id"
            placeholder="请输入用户ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="设备ID">
          <el-input
            v-model="searchForm.device_id"
            placeholder="请输入设备ID"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 会话统计 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ sessionStats?.total_sessions || 0 }}</div>
              <div class="stat-label">总会话数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ sessionStats?.active_sessions || 0 }}</div>
              <div class="stat-label">活跃会话</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ formatDuration(sessionStats?.avg_session_duration) }}</div>
              <div class="stat-label">平均时长</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ sessionStats?.period_days || 0 }}</div>
              <div class="stat-label">统计天数</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 会话日志表格 -->
    <el-table
      v-loading="loading"
      :data="sessionLogs"
      stripe
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="user_id" label="用户ID" width="100" />
      <el-table-column prop="username" label="用户名" width="120" />
      <el-table-column prop="device_id" label="设备ID" width="150" show-overflow-tooltip />
      <el-table-column prop="ip_address" label="IP地址" width="130" />
      <el-table-column prop="session_start_at" label="开始时间" width="160">
        <template #default="{ row }">
          {{ formatTime(row.session_start_at) }}
        </template>
      </el-table-column>
      <el-table-column prop="session_end_at" label="结束时间" width="160">
        <template #default="{ row }">
          {{ row.session_end_at ? formatTime(row.session_end_at) : '进行中' }}
        </template>
      </el-table-column>
      <el-table-column prop="duration_seconds" label="时长" width="100">
        <template #default="{ row }">
          {{ formatDuration(row.duration_seconds) }}
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="记录时间" width="160">
        <template #default="{ row }">
          {{ formatTime(row.created_at) }}
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { AdminLogService } from '@/api/adminApi'
import type { UserSessionLogResponse, UserSessionStatsResponse } from '@/api/model/categoryModel'

const loading = ref(false)
const sessionLogs = ref<UserSessionLogResponse[]>([])
const sessionStats = ref<UserSessionStatsResponse | null>(null)

const searchForm = reactive({
  user_id: '',
  device_id: '',
  dateRange: [] as string[]
})

const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 获取会话日志列表
const fetchSessionLogs = async () => {
  try {
    loading.value = true
    
    const params: any = {
      skip: (pagination.page - 1) * pagination.limit,
      limit: pagination.limit
    }

    if (searchForm.user_id) {
      params.user_id = parseInt(searchForm.user_id)
    }
    if (searchForm.device_id) {
      params.device_id = searchForm.device_id
    }
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.start_date = searchForm.dateRange[0]
      params.end_date = searchForm.dateRange[1]
    }

    const response = await AdminLogService.getUserSessions(params)
    // 根据API响应格式调整数据访问
    sessionLogs.value = response.items || response.data?.items || []
    pagination.total = response.total || response.data?.total || 0
  } catch (error) {
    console.error('获取会话日志失败:', error)
    ElMessage.error('获取会话日志失败')
  } finally {
    loading.value = false
  }
}

// 获取会话统计
const fetchSessionStats = async () => {
  try {
    const response = await AdminLogService.getUserSessionStats({ days: 30 })
    sessionStats.value = response.data || response
  } catch (error) {
    console.error('获取会话统计失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchSessionLogs()
}

// 重置
const handleReset = () => {
  searchForm.user_id = ''
  searchForm.device_id = ''
  searchForm.dateRange = []
  pagination.page = 1
  fetchSessionLogs()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  fetchSessionLogs()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchSessionLogs()
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}

// 格式化时长
const formatDuration = (seconds?: number) => {
  if (!seconds) return '-'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`
  } else {
    return `${secs}s`
  }
}

// 刷新数据
const refresh = () => {
  fetchSessionLogs()
  fetchSessionStats()
}

// 暴露刷新方法给父组件
defineExpose({
  refresh
})

onMounted(() => {
  fetchSessionLogs()
  fetchSessionStats()
})
</script>

<style lang="scss" scoped>
.user-session-logs {
  .search-form {
    margin-bottom: 20px;
    padding: 20px;
    background: var(--el-bg-color-page);
    border-radius: 8px;
  }

  .stats-cards {
    margin-bottom: 20px;

    .stat-card {
      .stat-content {
        text-align: center;
        padding: 10px 0;

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: var(--el-color-primary);
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-regular);
        }
      }
    }
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
