<template>
  <div class="audit-logs">
    <!-- 搜索筛选 -->
    <div class="search-form">
      <el-form :model="searchForm" inline>
        <el-form-item label="用户ID">
          <el-input
            v-model="searchForm.user_id"
            placeholder="请输入用户ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="操作类型">
          <el-select
            v-model="searchForm.action"
            placeholder="请选择操作类型"
            clearable
            style="width: 150px"
          >
            <el-option label="登录" value="login" />
            <el-option label="登出" value="logout" />
            <el-option label="创建" value="create" />
            <el-option label="更新" value="update" />
            <el-option label="删除" value="delete" />
            <el-option label="审核" value="approve" />
            <el-option label="拒绝" value="reject" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 审计日志表格 -->
    <el-table
      v-loading="loading"
      :data="auditLogs"
      stripe
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="user_id" label="用户ID" width="100" />
      <el-table-column prop="username" label="用户名" width="120" />
      <el-table-column prop="action" label="操作类型" width="120">
        <template #default="{ row }">
          <el-tag :type="getActionTagType(row.action)">
            {{ getActionLabel(row.action) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="resource_type" label="资源类型" width="120" />
      <el-table-column prop="resource_id" label="资源ID" width="100" />
      <el-table-column prop="description" label="操作描述" min-width="200" show-overflow-tooltip />
      <el-table-column prop="ip_address" label="IP地址" width="130" />
      <el-table-column prop="user_agent" label="用户代理" width="150" show-overflow-tooltip />
      <el-table-column prop="created_at" label="操作时间" width="160">
        <template #default="{ row }">
          {{ formatTime(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="showAuditDetail(row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.limit"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 审计详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="审计日志详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="selectedAudit" class="audit-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="操作时间">
            {{ formatTime(selectedAudit.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="用户">
            {{ selectedAudit.username }} (ID: {{ selectedAudit.user_id }})
          </el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getActionTagType(selectedAudit.action)">
              {{ getActionLabel(selectedAudit.action) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="资源">
            {{ selectedAudit.resource_type }} (ID: {{ selectedAudit.resource_id }})
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">
            {{ selectedAudit.ip_address }}
          </el-descriptions-item>
          <el-descriptions-item label="会话ID">
            {{ selectedAudit.session_id || '-' }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="audit-description">
          <h4>操作描述</h4>
          <p>{{ selectedAudit.description }}</p>
        </div>

        <div class="audit-user-agent">
          <h4>用户代理</h4>
          <p>{{ selectedAudit.user_agent }}</p>
        </div>

        <div v-if="selectedAudit.changes" class="audit-changes">
          <h4>变更内容</h4>
          <pre>{{ JSON.stringify(selectedAudit.changes, null, 2) }}</pre>
        </div>

        <div v-if="selectedAudit.extra_data" class="audit-extra">
          <h4>额外数据</h4>
          <pre>{{ JSON.stringify(selectedAudit.extra_data, null, 2) }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import { AdminLogService } from '@/api/adminApi'

const loading = ref(false)
const auditLogs = ref<any[]>([])
const detailDialogVisible = ref(false)
const selectedAudit = ref<any>(null)

const searchForm = reactive({
  user_id: '',
  action: '',
  dateRange: [] as string[]
})

const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 获取审计日志列表
const fetchAuditLogs = async () => {
  try {
    loading.value = true
    
    const params: any = {
      skip: (pagination.page - 1) * pagination.limit,
      limit: pagination.limit
    }

    if (searchForm.user_id) {
      params.user_id = parseInt(searchForm.user_id)
    }
    if (searchForm.action) {
      params.action = searchForm.action
    }
    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.start_date = searchForm.dateRange[0]
      params.end_date = searchForm.dateRange[1]
    }

    const response = await AdminLogService.getAuditLogs(params)
    // 根据API响应格式调整数据访问
    auditLogs.value = response.items || response.data?.items || []
    pagination.total = response.total || response.data?.total || 0
  } catch (error) {
    console.error('获取审计日志失败:', error)
    ElMessage.error('获取审计日志失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchAuditLogs()
}

// 重置
const handleReset = () => {
  searchForm.user_id = ''
  searchForm.action = ''
  searchForm.dateRange = []
  pagination.page = 1
  fetchAuditLogs()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  fetchAuditLogs()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchAuditLogs()
}

// 显示审计详情
const showAuditDetail = (audit: any) => {
  selectedAudit.value = audit
  detailDialogVisible.value = true
}

// 获取操作类型标签类型
const getActionTagType = (action: string) => {
  switch (action?.toLowerCase()) {
    case 'login':
      return 'success'
    case 'logout':
      return 'info'
    case 'create':
      return 'success'
    case 'update':
      return 'warning'
    case 'delete':
      return 'danger'
    case 'approve':
      return 'success'
    case 'reject':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取操作类型标签
const getActionLabel = (action: string) => {
  const labels: Record<string, string> = {
    login: '登录',
    logout: '登出',
    create: '创建',
    update: '更新',
    delete: '删除',
    approve: '审核通过',
    reject: '审核拒绝'
  }
  return labels[action] || action
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString('zh-CN')
}

// 刷新数据
const refresh = () => {
  fetchAuditLogs()
}

// 暴露刷新方法给父组件
defineExpose({
  refresh
})

onMounted(() => {
  fetchAuditLogs()
})
</script>

<style lang="scss" scoped>
.audit-logs {
  .search-form {
    margin-bottom: 20px;
    padding: 20px;
    background: var(--el-bg-color-page);
    border-radius: 8px;
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }

  .audit-detail {
    .audit-description,
    .audit-user-agent,
    .audit-changes,
    .audit-extra {
      margin-top: 20px;

      h4 {
        margin-bottom: 10px;
        color: var(--el-text-color-primary);
      }

      p {
        background: var(--el-bg-color-page);
        padding: 10px;
        border-radius: 4px;
        margin: 0;
        word-wrap: break-word;
      }

      pre {
        background: var(--el-bg-color-page);
        padding: 15px;
        border-radius: 4px;
        font-size: 12px;
        line-height: 1.5;
        max-height: 300px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
}
</style>
