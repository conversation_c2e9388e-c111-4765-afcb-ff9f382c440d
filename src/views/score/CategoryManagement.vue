<template>
  <div class="category-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>分类管理</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增分类
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="分类名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入分类名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="父分类">
          <el-select v-model="searchForm.parent_id" placeholder="请选择父分类" clearable>
            <el-option label="无父分类" :value="null" />
            <el-option
              v-for="category in parentCategories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.only_active" placeholder="请选择状态" clearable>
            <el-option label="启用" :value="true" />
            <el-option label="禁用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="分类名称" min-width="200" />
        <el-table-column prop="slug" label="标识符" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200">
          <template #default="{ row }">
            {{ row.description || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="sort_order" label="排序" width="80" />
        <el-table-column prop="is_active" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="score_count" label="谱曲数量" width="100" />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-dropdown @command="(command) => handleAction(command, row)">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="addChild">添加子分类</el-dropdown-item>
                  <el-dropdown-item command="toggle">
                    {{ row.is_active ? '禁用' : '启用' }}
                  </el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchData"
          @current-change="fetchData"
        />
      </div>
    </el-card>

    <!-- 新增/编辑分类对话框 -->
    <el-dialog v-model="dialogVisible" :title="isEdit ? '编辑分类' : '新增分类'" width="500px">
      <el-form :model="categoryForm" :rules="categoryRules" ref="categoryFormRef" label-width="100px">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="标识符" prop="slug">
          <el-input v-model="categoryForm.slug" placeholder="请输入标识符（英文）" />
        </el-form-item>
        <el-form-item label="父分类" prop="parent_id">
          <el-select v-model="categoryForm.parent_id" placeholder="请选择父分类" style="width: 100%">
            <el-option label="无父分类" :value="null" />
            <el-option
              v-for="category in parentCategories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
              :disabled="isEdit && category.id === categoryForm.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="categoryForm.description" type="textarea" :rows="3" placeholder="请输入分类描述" />
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number v-model="categoryForm.sort_order" :min="0" :max="9999" style="width: 100%" />
        </el-form-item>
        <el-form-item label="状态" prop="is_active">
          <el-switch v-model="categoryForm.is_active" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">确定</el-button>
      </template>
    </el-dialog>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="viewDialogVisible" title="分类详情" width="600px">
      <div v-if="currentCategory" class="category-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ currentCategory.id }}</el-descriptions-item>
          <el-descriptions-item label="分类名称">{{ currentCategory.name }}</el-descriptions-item>
          <el-descriptions-item label="标识符">{{ currentCategory.slug }}</el-descriptions-item>
          <el-descriptions-item label="父分类">{{ currentCategory.parent?.name || '无' }}</el-descriptions-item>
          <el-descriptions-item label="排序">{{ currentCategory.sort_order }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentCategory.is_active ? 'success' : 'danger'">
              {{ currentCategory.is_active ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="谱曲数量">{{ currentCategory.score_count }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(currentCategory.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">{{ currentCategory.description || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="viewDialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Plus, ArrowDown } from '@element-plus/icons-vue'
  import { CategoryService } from '@/api/categoryApi'
  import type { CategoryDetailResponse } from '@/api/model/categoryModel'

  // 响应式数据
  const loading = ref(false)
  const tableData = ref<CategoryDetailResponse[]>([])
  const selectedRows = ref<CategoryDetailResponse[]>([])
  const dialogVisible = ref(false)
  const viewDialogVisible = ref(false)
  const isEdit = ref(false)
  const submitLoading = ref(false)
  const currentCategory = ref<CategoryDetailResponse | null>(null)
  const categoryFormRef = ref()

  // 搜索表单
  const searchForm = reactive({
    name: '',
    parent_id: null as number | null,
    only_active: null as boolean | null
  })

  // 分类表单
  const categoryForm = reactive({
    id: null as number | null,
    name: '',
    slug: '',
    parent_id: null as number | null,
    description: '',
    sort_order: 0,
    is_active: true
  })

  // 分页数据
  const pagination = reactive({
    page: 1,
    limit: 20,
    total: 0
  })

  // 表单验证规则
  const categoryRules = {
    name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
    slug: [{ required: true, message: '请输入标识符', trigger: 'blur' }]
  }

  // 计算属性 - 父分类选项
  const parentCategories = computed(() => {
    return tableData.value.filter(item => !item.parent_id)
  })

  // 获取数据
  const fetchData = async () => {
    loading.value = true
    try {
      const params = {
        skip: (pagination.page - 1) * pagination.limit,
        limit: pagination.limit,
        ...searchForm
      }

      const response = await CategoryService.getCategories(params)
      tableData.value = response
      pagination.total = response.length // 注意：API返回的是数组，不是分页对象
    } catch {
      ElMessage.error('获取数据失败')
    } finally {
      loading.value = false
    }
  }

  // 事件处理
  const handleSearch = () => {
    pagination.page = 1
    fetchData()
  }

  const handleReset = () => {
    Object.assign(searchForm, {
      name: '',
      parent_id: null,
      only_active: null
    })
    handleSearch()
  }

  const handleSelectionChange = (selection: CategoryDetailResponse[]) => {
    selectedRows.value = selection
  }

  const handleAdd = () => {
    isEdit.value = false
    Object.assign(categoryForm, {
      id: null,
      name: '',
      slug: '',
      parent_id: null,
      description: '',
      sort_order: 0,
      is_active: true
    })
    dialogVisible.value = true
  }

  const handleView = (row: CategoryDetailResponse) => {
    currentCategory.value = row
    viewDialogVisible.value = true
  }

  const handleEdit = (row: CategoryDetailResponse) => {
    isEdit.value = true
    Object.assign(categoryForm, {
      id: row.id,
      name: row.name,
      slug: row.slug,
      parent_id: row.parent_id,
      description: row.description || '',
      sort_order: row.sort_order,
      is_active: row.is_active
    })
    dialogVisible.value = true
  }

  const handleAction = async (command: string, row: CategoryDetailResponse) => {
    switch (command) {
      case 'addChild':
        handleAddChild(row)
        break
      case 'toggle':
        await handleToggleStatus(row)
        break
      case 'delete':
        await handleDelete(row)
        break
    }
  }

  const handleAddChild = (row: CategoryDetailResponse) => {
    isEdit.value = false
    Object.assign(categoryForm, {
      id: null,
      name: '',
      slug: '',
      parent_id: row.id,
      description: '',
      sort_order: 0,
      is_active: true
    })
    dialogVisible.value = true
  }

  const handleToggleStatus = async (row: CategoryDetailResponse) => {
    try {
      await CategoryService.updateCategory(row.id, { is_active: !row.is_active })
      ElMessage.success('状态更新成功')
      fetchData()
    } catch {
      ElMessage.error('状态更新失败')
    }
  }

  const handleDelete = async (row: CategoryDetailResponse) => {
    try {
      await ElMessageBox.confirm('确定要删除这个分类吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await CategoryService.deleteCategory(row.id)
      ElMessage.success('删除成功')
      fetchData()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  const handleSubmit = async () => {
    if (!categoryFormRef.value) return

    await categoryFormRef.value.validate(async (valid: boolean) => {
      if (valid) {
        submitLoading.value = true
        try {
          if (isEdit.value) {
            await CategoryService.updateCategory(categoryForm.id!, categoryForm)
            ElMessage.success('更新成功')
          } else {
            await CategoryService.createCategory(categoryForm)
            ElMessage.success('创建成功')
          }
          dialogVisible.value = false
          fetchData()
        } catch {
          ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
        } finally {
          submitLoading.value = false
        }
      }
    })
  }

  // 工具函数
  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 生命周期
  onMounted(() => {
    fetchData()
  })
</script>

<style lang="scss" scoped>
  .category-management {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .search-form {
      margin-bottom: 20px;
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }

    .category-detail {
      padding: 20px 0;
    }
  }
</style>
