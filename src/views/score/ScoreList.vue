<template>
  <div class="score-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>谱曲管理</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增谱曲
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="标题">
          <el-input
            v-model="searchForm.title"
            placeholder="请输入谱曲标题"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="searchForm.category_id" placeholder="请选择分类" clearable>
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="待审核" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="私有" value="private" />
            <el-option label="草稿" value="draft" />
          </el-select>
        </el-form-item>
        <el-form-item label="难度">
          <el-select v-model="searchForm.difficulty" placeholder="请选择难度" clearable>
            <el-option label="初学者" value="beginner" />
            <el-option label="简单" value="easy" />
            <el-option label="中等" value="medium" />
            <el-option label="困难" value="hard" />
            <el-option label="专家" value="expert" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="difficulty" label="难度" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.difficulty" :type="getDifficultyTagType(row.difficulty)">
              {{ getDifficultyLabel(row.difficulty) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="uploader.username" label="上传者" width="120" />
        <el-table-column prop="view_count" label="浏览量" width="100" />
        <el-table-column prop="favorite_count" label="收藏数" width="100" />
        <el-table-column prop="average_rating" label="评分" width="100">
          <template #default="{ row }">
            <el-rate
              v-model="row.average_rating"
              disabled
              show-score
              text-color="#ff9900"
              score-template="{value}"
            />
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Plus } from '@element-plus/icons-vue'
  import { ScoreService } from '@/api/scoreApi'
  import { CategoryService } from '@/api/categoryApi'
  import { AdminScoreService } from '@/api/adminApi'
  import type { ScoreResponse } from '@/api/model/scoreModel'
  import type { CategoryDetailResponse } from '@/api/model/categoryModel'

  // 响应式数据
  const loading = ref(false)
  const tableData = ref<ScoreResponse[]>([])
  const categories = ref<CategoryDetailResponse[]>([])
  const selectedRows = ref<ScoreResponse[]>([])

  // 搜索表单
  const searchForm = reactive({
    title: '',
    category_id: undefined as number | undefined,
    status: '',
    difficulty: ''
  })

  // 分页数据
  const pagination = reactive({
    page: 1,
    limit: 20,
    total: 0
  })

  // 获取数据
  const fetchData = async () => {
    loading.value = true
    try {
      const params = {
        skip: (pagination.page - 1) * pagination.limit,
        limit: pagination.limit,
        ...searchForm
      }

      const response = await AdminScoreService.getAllScores(params)
      tableData.value = response.items
      pagination.total = response.total
    } catch {
      ElMessage.error('获取数据失败')
    } finally {
      loading.value = false
    }
  }

  // 获取分类列表
  const fetchCategories = async () => {
    try {
      const response = await CategoryService.getCategories()
      categories.value = response
    } catch {
      ElMessage.error('获取分类失败')
    }
  }

  // 事件处理
  const handleSearch = () => {
    pagination.page = 1
    fetchData()
  }

  const handleReset = () => {
    Object.assign(searchForm, {
      title: '',
      category_id: undefined,
      status: '',
      difficulty: ''
    })
    handleSearch()
  }

  const handleAdd = () => {
    // 跳转到新增页面
    console.log('新增谱曲')
  }

  const handleView = (row: ScoreResponse) => {
    // 跳转到详情页面
    console.log('查看谱曲', row)
  }

  const handleEdit = (row: ScoreResponse) => {
    // 跳转到编辑页面
    console.log('编辑谱曲', row)
  }

  const handleDelete = async (row: ScoreResponse) => {
    try {
      await ElMessageBox.confirm('确定要删除这个谱曲吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await ScoreService.deleteScore(row.id)
      ElMessage.success('删除成功')
      fetchData()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  const handleSelectionChange = (selection: ScoreResponse[]) => {
    selectedRows.value = selection
  }

  const handleSizeChange = (size: number) => {
    pagination.limit = size
    pagination.page = 1
    fetchData()
  }

  const handleCurrentChange = (page: number) => {
    pagination.page = page
    fetchData()
  }

  // 工具函数
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const getTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      TXT: 'TXT谱',
      MIDI_REF: 'MIDI文件',
      EXTERNAL_LINK: '外部链接'
    }
    return labels[type] || type
  }

  const getTypeTagType = (type: string) => {
    const types: Record<string, string> = {
      TXT: 'primary',
      MIDI_REF: 'success',
      EXTERNAL_LINK: 'warning'
    }
    return types[type] || ''
  }

  const getDifficultyLabel = (difficulty: string) => {
    const labels: Record<string, string> = {
      beginner: '初学者',
      easy: '简单',
      medium: '中等',
      hard: '困难',
      expert: '专家'
    }
    return labels[difficulty] || difficulty
  }

  const getDifficultyTagType = (difficulty: string) => {
    const types: Record<string, string> = {
      beginner: 'info',
      easy: 'success',
      medium: 'warning',
      hard: 'danger',
      expert: 'danger'
    }
    return types[difficulty] || ''
  }

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      pending: '待审核',
      approved: '已通过',
      rejected: '已拒绝',
      private: '私有',
      draft: '草稿'
    }
    return labels[status] || status
  }

  const getStatusTagType = (status: string) => {
    const types: Record<string, string> = {
      pending: 'warning',
      approved: 'success',
      rejected: 'danger',
      private: 'info',
      draft: 'info'
    }
    return types[status] || ''
  }

  // 生命周期
  onMounted(() => {
    fetchData()
    fetchCategories()
  })
</script>

<style scoped>
  .score-list {
    padding: 20px;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .search-form {
    margin-bottom: 20px;
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }
</style>
