<template>
  <div class="score-detail">
    <el-card v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>谱曲详情</span>
          <div>
            <el-button @click="goBack">返回</el-button>
            <el-button type="primary" @click="handleEdit">编辑</el-button>
          </div>
        </div>
      </template>

      <div v-if="scoreData" class="score-content">
        <!-- 基本信息 -->
        <el-row :gutter="20">
          <el-col :span="16">
            <el-descriptions title="基本信息" :column="2" border>
              <el-descriptions-item label="标题">{{ scoreData.title }}</el-descriptions-item>
              <el-descriptions-item label="类型">
                <el-tag :type="getTypeTagType(scoreData.type)">
                  {{ getTypeLabel(scoreData.type) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="难度">
                <el-tag
                  v-if="scoreData.difficulty"
                  :type="getDifficultyTagType(scoreData.difficulty)"
                >
                  {{ getDifficultyLabel(scoreData.difficulty) }}
                </el-tag>
                <span v-else>-</span>
              </el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getStatusTagType(scoreData.status)">
                  {{ getStatusLabel(scoreData.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="分类">
                {{ scoreData.category?.name || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="上传者">
                {{ scoreData.uploader?.username || '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="是否付费">
                <el-tag :type="scoreData.is_premium_only ? 'warning' : 'success'">
                  {{ scoreData.is_premium_only ? '付费' : '免费' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="时长">
                {{ scoreData.duration_seconds ? `${scoreData.duration_seconds}秒` : '-' }}
              </el-descriptions-item>
              <el-descriptions-item label="浏览量">{{ scoreData.view_count }}</el-descriptions-item>
              <el-descriptions-item label="下载量">{{
                scoreData.download_count
              }}</el-descriptions-item>
              <el-descriptions-item label="收藏数">{{
                scoreData.favorite_count
              }}</el-descriptions-item>
              <el-descriptions-item label="评论数">{{
                scoreData.comment_count
              }}</el-descriptions-item>
              <el-descriptions-item label="平均评分">
                <el-rate
                  v-model="scoreData.average_rating"
                  disabled
                  show-score
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </el-descriptions-item>
              <el-descriptions-item label="评分次数">{{
                scoreData.rating_count
              }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">
                {{ formatDate(scoreData.created_at) }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatDate(scoreData.updated_at) }}
              </el-descriptions-item>
            </el-descriptions>

            <!-- 描述 -->
            <el-card class="description-card" header="描述">
              <div v-if="scoreData.description" class="description-content">
                {{ scoreData.description }}
              </div>
              <div v-else class="no-description">暂无描述</div>
            </el-card>

            <!-- 标签 -->
            <el-card v-if="scoreData.tags && scoreData.tags.length" class="tags-card" header="标签">
              <el-tag v-for="tag in scoreData.tags" :key="tag" class="tag-item" type="info">
                {{ tag }}
              </el-tag>
            </el-card>

            <!-- TXT内容 -->
            <el-card
              v-if="scoreData.type === 'TXT' && scoreData.txt_content"
              class="content-card"
              header="TXT内容"
            >
              <pre class="txt-content">{{ scoreData.txt_content }}</pre>
            </el-card>

            <!-- MIDI文件信息 -->
            <el-card
              v-if="scoreData.type === 'MIDI_REF'"
              class="content-card"
              header="MIDI文件信息"
            >
              <el-descriptions :column="1" border>
                <el-descriptions-item label="存储类型">
                  {{ scoreData.midi_storage_type }}
                </el-descriptions-item>
                <el-descriptions-item label="文件路径/URL">
                  {{ scoreData.midi_path_or_url }}
                </el-descriptions-item>
                <el-descriptions-item label="原始文件名">
                  {{ scoreData.original_midi_filename }}
                </el-descriptions-item>
              </el-descriptions>
            </el-card>

            <!-- 外部链接 -->
            <el-card
              v-if="scoreData.type === 'EXTERNAL_LINK'"
              class="content-card"
              header="外部链接"
            >
              <el-link :href="scoreData.external_url" target="_blank" type="primary">
                {{ scoreData.external_url }}
              </el-link>
            </el-card>

            <!-- 审核信息 -->
            <el-card
              v-if="scoreData.approved_at || scoreData.approval_remarks"
              class="approval-card"
              header="审核信息"
            >
              <el-descriptions :column="1" border>
                <el-descriptions-item v-if="scoreData.approver" label="审核人">
                  {{ scoreData.approver.username }}
                </el-descriptions-item>
                <el-descriptions-item v-if="scoreData.approved_at" label="审核时间">
                  {{ formatDate(scoreData.approved_at) }}
                </el-descriptions-item>
                <el-descriptions-item v-if="scoreData.approval_remarks" label="审核备注">
                  {{ scoreData.approval_remarks }}
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>

          <el-col :span="8">
            <!-- 封面图片 -->
            <el-card header="封面图片">
              <div v-if="scoreData.cover_image_url" class="cover-image">
                <el-image
                  :src="scoreData.cover_image_url"
                  fit="cover"
                  style="width: 100%; height: 200px"
                  :preview-src-list="[scoreData.cover_image_url]"
                />
              </div>
              <div v-else class="no-cover">
                <el-icon size="60"><Picture /></el-icon>
                <p>暂无封面</p>
              </div>
            </el-card>

            <!-- 操作按钮 -->
            <el-card header="操作" class="actions-card">
              <el-space direction="vertical" style="width: 100%">
                <el-button type="primary" style="width: 100%" @click="handleFavorite">
                  <el-icon><Star /></el-icon>
                  {{ isFavorited ? '取消收藏' : '收藏' }}
                </el-button>
                <el-button type="success" style="width: 100%" @click="handleDownload">
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
                <el-button
                  v-if="canApprove"
                  type="warning"
                  style="width: 100%"
                  @click="handleApprove"
                >
                  <el-icon><Check /></el-icon>
                  审核通过
                </el-button>
                <el-button v-if="canReject" type="danger" style="width: 100%" @click="handleReject">
                  <el-icon><Close /></el-icon>
                  审核拒绝
                </el-button>
              </el-space>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Picture, Star, Download, Check, Close } from '@element-plus/icons-vue'
  import { ScoreService } from '@/api/scoreApi'
  import { AdminScoreService } from '@/api/adminApi'
  import type { ScoreDetailResponse } from '@/api/model/scoreModel'

  const route = useRoute()
  const router = useRouter()

  // 响应式数据
  const loading = ref(false)
  const scoreData = ref<ScoreDetailResponse | null>(null)
  const isFavorited = ref(false)

  // 计算属性
  const canApprove = computed(() => {
    return scoreData.value?.status === 'pending'
  })

  const canReject = computed(() => {
    return scoreData.value?.status === 'pending'
  })

  // 获取谱曲详情
  const fetchScoreDetail = async () => {
    const scoreId = Number(route.params.id)
    if (!scoreId) {
      ElMessage.error('无效的谱曲ID')
      return
    }

    loading.value = true
    try {
      scoreData.value = await ScoreService.getScoreDetails(scoreId)

      // 检查是否已收藏
      const favoriteStatus = await ScoreService.checkScoreIsFavorited(scoreId)
      isFavorited.value = favoriteStatus.is_favorited
    } catch {
      ElMessage.error('获取谱曲详情失败')
    } finally {
      loading.value = false
    }
  }

  // 事件处理
  const goBack = () => {
    router.back()
  }

  const handleEdit = () => {
    router.push(`/score/edit/${scoreData.value?.id}`)
  }

  const handleFavorite = async () => {
    if (!scoreData.value) return

    try {
      if (isFavorited.value) {
        await ScoreService.unfavoriteScore(scoreData.value.id)
        ElMessage.success('取消收藏成功')
      } else {
        await ScoreService.favoriteScore(scoreData.value.id)
        ElMessage.success('收藏成功')
      }
      isFavorited.value = !isFavorited.value
    } catch {
      ElMessage.error('操作失败')
    }
  }

  const handleDownload = () => {
    ElMessage.info('下载功能待实现')
  }

  const handleApprove = async () => {
    if (!scoreData.value) return

    try {
      await ElMessageBox.confirm('确定要审核通过这个谱曲吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      await AdminScoreService.updateScoreStatus(scoreData.value.id, {
        status: 'approved',
        approval_remarks: '审核通过'
      })

      ElMessage.success('审核通过成功')
      fetchScoreDetail()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('审核失败')
      }
    }
  }

  const handleReject = async () => {
    if (!scoreData.value) return

    try {
      const { value: remarks } = await ElMessageBox.prompt('请输入拒绝原因', '审核拒绝', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /.+/,
        inputErrorMessage: '请输入拒绝原因'
      })

      await AdminScoreService.updateScoreStatus(scoreData.value.id, {
        status: 'rejected',
        approval_remarks: remarks
      })

      ElMessage.success('审核拒绝成功')
      fetchScoreDetail()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('审核失败')
      }
    }
  }

  // 工具函数
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const getTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      TXT: 'TXT谱',
      MIDI_REF: 'MIDI文件',
      EXTERNAL_LINK: '外部链接'
    }
    return labels[type] || type
  }

  const getTypeTagType = (type: string) => {
    const types: Record<string, string> = {
      TXT: 'primary',
      MIDI_REF: 'success',
      EXTERNAL_LINK: 'warning'
    }
    return types[type] || ''
  }

  const getDifficultyLabel = (difficulty: string) => {
    const labels: Record<string, string> = {
      beginner: '初学者',
      easy: '简单',
      medium: '中等',
      hard: '困难',
      expert: '专家'
    }
    return labels[difficulty] || difficulty
  }

  const getDifficultyTagType = (difficulty: string) => {
    const types: Record<string, string> = {
      beginner: 'info',
      easy: 'success',
      medium: 'warning',
      hard: 'danger',
      expert: 'danger'
    }
    return types[difficulty] || ''
  }

  const getStatusLabel = (status: string) => {
    const labels: Record<string, string> = {
      pending: '待审核',
      approved: '已通过',
      rejected: '已拒绝',
      private: '私有',
      draft: '草稿'
    }
    return labels[status] || status
  }

  const getStatusTagType = (status: string) => {
    const types: Record<string, string> = {
      pending: 'warning',
      approved: 'success',
      rejected: 'danger',
      private: 'info',
      draft: 'info'
    }
    return types[status] || ''
  }

  // 生命周期
  onMounted(() => {
    fetchScoreDetail()
  })
</script>

<style scoped>
  .score-detail {
    padding: 20px;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .description-card,
  .tags-card,
  .content-card,
  .approval-card,
  .actions-card {
    margin-top: 20px;
  }

  .description-content {
    line-height: 1.6;
    white-space: pre-wrap;
  }

  .no-description {
    padding: 20px;
    color: #999;
    text-align: center;
  }

  .tag-item {
    margin-right: 8px;
    margin-bottom: 8px;
  }

  .txt-content {
    max-height: 400px;
    padding: 15px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    white-space: pre-wrap;
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  .cover-image {
    text-align: center;
  }

  .no-cover {
    padding: 40px 20px;
    color: #999;
    text-align: center;
  }

  .no-cover p {
    margin-top: 10px;
  }
</style>
