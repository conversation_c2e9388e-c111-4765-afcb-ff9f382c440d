<template>
  <div class="login">
    <div class="left-wrap">
      <LoginLeftView></LoginLeftView>
    </div>
    <div class="right-wrap">
      <div class="top-right-wrap">
        <div class="btn theme-btn" @click="toggleTheme">
          <i class="iconfont-sys">
            {{ isDark ? '&#xe6b5;' : '&#xe725;' }}
          </i>
        </div>
        <el-dropdown @command="changeLanguage" popper-class="langDropDownStyle">
          <div class="btn language-btn">
            <i class="iconfont-sys icon-language">&#xe611;</i>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <div v-for="lang in languageOptions" :key="lang.value" class="lang-btn-item">
                <el-dropdown-item
                  :command="lang.value"
                  :class="{ 'is-selected': locale === lang.value }"
                >
                  <span class="menu-txt">{{ lang.label }}</span>
                  <i v-if="locale === lang.value" class="iconfont-sys icon-check">&#xe621;</i>
                </el-dropdown-item>
              </div>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="header">
        <ArtLogo class="icon" />
        <h1>{{ systemName }}</h1>
      </div>
      <div class="login-wrap">
        <div class="form">
          <h3 class="title">{{ $t('login.title') }}</h3>
          <p class="sub-title">{{ $t('login.subTitle') }}</p>
          <el-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            @keyup.enter="handleSubmit"
            style="margin-top: 25px"
          >
            <!-- 登录方式选择 -->
            <el-form-item prop="loginType">
              <el-radio-group v-model="formData.loginType" @change="handleLoginTypeChange">
                <el-radio value="admin">管理员登录</el-radio>
                <el-radio value="reseller">代理商登录</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 用户名密码登录 (管理员或代理商) -->
            <template v-if="formData.loginType === 'admin' || formData.loginType === 'reseller'">
              <el-form-item prop="username">
                <el-input placeholder="请输入用户名" v-model.trim="formData.username" />
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                  placeholder="请输入密码"
                  v-model.trim="formData.password"
                  type="password"
                  radius="8px"
                  autocomplete="off"
                />
              </el-form-item>
            </template>


            <div class="drag-verify">
              <div class="drag-verify-content" :class="{ error: !isPassing && isClickPass }">
                <ArtDragVerify
                  ref="dragVerify"
                  v-model:value="isPassing"
                  :width="width < 500 ? 328 : 438"
                  :text="$t('login.sliderText')"
                  textColor="var(--art-gray-800)"
                  :successText="$t('login.sliderSuccessText')"
                  :progressBarBg="getCssVariable('--el-color-primary')"
                  background="var(--art-gray-200)"
                  handlerBg="var(--art-main-bg-color)"
                  @pass="onPass"
                />
              </div>
              <p class="error-text" :class="{ 'show-error-text': !isPassing && isClickPass }">{{
                $t('login.placeholder[2]')
              }}</p>
            </div>

            <div class="forget-password">
              <el-checkbox v-model="formData.rememberPassword">{{
                $t('login.rememberPwd')
              }}</el-checkbox>
              <router-link to="/forget-password">{{ $t('login.forgetPwd') }}</router-link>
            </div>

            <div style="margin-top: 30px">
              <el-button
                class="login-btn"
                type="primary"
                @click="handleSubmit"
                :loading="loading"
                v-ripple
              >
                {{ $t('login.btnText') }}
              </el-button>
            </div>

            <div class="footer">
              <p>
                {{ $t('login.noAccount') }}
                <router-link to="/register">{{ $t('login.register') }}</router-link>
              </p>
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import AppConfig from '@/config'
  import { ElMessage, ElNotification } from 'element-plus'
  import { useUserStore } from '@/store/modules/user'
  import { HOME_PAGE } from '@/router/routesAlias'

  import { getCssVariable } from '@/utils/colors'
  import { languageOptions } from '@/language'
  import { LanguageEnum, SystemThemeEnum } from '@/enums/appEnum'
  import { useI18n } from 'vue-i18n'

  const { t } = useI18n()
  import { useSettingStore } from '@/store/modules/setting'
  import type { FormInstance, FormRules } from 'element-plus'

  const settingStore = useSettingStore()
  const { isDark, systemThemeType } = storeToRefs(settingStore)

  const dragVerify = ref()

  const userStore = useUserStore()
  const router = useRouter()
  const isPassing = ref(false)
  const isClickPass = ref(false)

  const systemName = AppConfig.systemInfo.name
  const formRef = ref<FormInstance>()

  const formData = reactive({
    loginType: 'admin', // 'admin' | 'reseller'
    username: '',
    password: '',
    rememberPassword: true
  })

  const rules = computed<FormRules>(() => {
    const baseRules: FormRules = {
      loginType: [{ required: true, message: '请选择登录方式', trigger: 'change' }],
      username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
      password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
    }
    return baseRules
  })

  const loading = ref(false)
  const { width } = useWindowSize()

  // 处理登录方式变化
  const handleLoginTypeChange = () => {
    // 清空表单验证
    formRef.value?.clearValidate()
  }

  const onPass = () => {}

  const handleSubmit = async () => {
    if (!formRef.value) return

    await formRef.value.validate(async (valid) => {
      if (valid) {
        if (!isPassing.value) {
          isClickPass.value = true
          return
        }

        loading.value = true

        try {
          let response

          if (formData.loginType === 'admin') {
            response = await AuthService.adminLogin({
              username: formData.username,
              password: formData.password
            })
          } else if (formData.loginType === 'reseller') {
            response = await AuthService.resellerLogin({
              username: formData.username,
              password: formData.password
            })
          }

          // 保存token
          userStore.setToken(response.access_token, response.refresh_token)

          // 转换用户信息格式以适配store
          const storeUserInfo = {
            userId: response.user_info.id,
            userName: response.user_info.username,
            roles: [response.user_info.role], // API返回的角色：admin, reseller, user
            buttons: [] // 暂时为空，后续可根据角色配置
          }

          // 保存用户信息
          userStore.setUserInfo(storeUserInfo)
          userStore.setLoginStatus(true)

          // 登录成功提示
          showLoginSuccessNotice()

          // 跳转到首页
          router.push(HOME_PAGE)
        } catch (error: any) {
          ElMessage.error(error.response?.data?.detail || '登录失败')
          resetDragVerify()
        } finally {
          loading.value = false
        }
      }
    })
  }

  // 重置拖拽验证
  const resetDragVerify = () => {
    dragVerify.value.reset()
  }

  // 登录成功提示
  const showLoginSuccessNotice = () => {
    setTimeout(() => {
      ElNotification({
        title: t('login.success.title'),
        type: 'success',
        duration: 2500,
        zIndex: 10000,
        message: `${t('login.success.message')}, ${systemName}!`
      })
    }, 150)
  }

  // 切换语言
  const { locale } = useI18n()

  const changeLanguage = (lang: LanguageEnum) => {
    if (locale.value === lang) return
    locale.value = lang
    userStore.setLanguage(lang)
  }

  // 切换主题
  import { useTheme } from '@/composables/useTheme'
  import { AuthService } from '@/api/usersApi'

  const toggleTheme = () => {
    let { LIGHT, DARK } = SystemThemeEnum
    useTheme().switchThemeStyles(systemThemeType.value === LIGHT ? DARK : LIGHT)
  }
</script>

<style lang="scss" scoped>
  @use './index';
</style>
