<template>
  <div class="dashboard-overview">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :xs="24" :sm="12" :md="6" v-for="stat in stats" :key="stat.key">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon" :style="{ backgroundColor: stat.color }">
              <i class="iconfont-sys" v-html="stat.icon"></i>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <el-col :xs="24" :lg="12">
        <el-card header="谱曲上传趋势">
          <div ref="uploadTrendChart" style="height: 300px"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="12">
        <el-card header="用户注册趋势">
          <div ref="userTrendChart" style="height: 300px"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <el-col :xs="24" :lg="8">
        <el-card header="谱曲分类分布">
          <div ref="categoryChart" style="height: 300px"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="8">
        <el-card header="用户角色分布">
          <div ref="roleChart" style="height: 300px"></div>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="8">
        <el-card header="谱曲状态分布">
          <div ref="statusChart" style="height: 300px"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最新数据 -->
    <el-row :gutter="20" class="latest-section">
      <el-col :xs="24" :lg="12">
        <el-card header="最新谱曲">
          <el-table :data="latestScores" style="width: 100%">
            <el-table-column prop="title" label="标题" show-overflow-tooltip />
            <el-table-column prop="uploader.username" label="上传者" width="100" />
            <el-table-column prop="created_at" label="上传时间" width="120">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      <el-col :xs="24" :lg="12">
        <el-card header="最新用户">
          <el-table :data="latestUsers" style="width: 100%">
            <el-table-column prop="username" label="用户名" />
            <el-table-column prop="role" label="角色" width="80">
              <template #default="{ row }">
                <el-tag :type="getRoleTagType(row.role)" size="small">
                  {{ getRoleLabel(row.role) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="注册时间" width="120">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, nextTick } from 'vue'
  import * as echarts from 'echarts'
  import { ScoreService } from '@/api/scoreApi'
  import { AdminUserService } from '@/api/adminApi'

  // 图表引用
  const uploadTrendChart = ref<HTMLElement>()
  const userTrendChart = ref<HTMLElement>()
  const categoryChart = ref<HTMLElement>()
  const roleChart = ref<HTMLElement>()
  const statusChart = ref<HTMLElement>()

  // 数据
  const stats = ref([
    {
      key: 'totalScores',
      label: '总谱曲数',
      value: '0',
      icon: '&#xe7ae;',
      color: '#409EFF'
    },
    {
      key: 'totalUsers',
      label: '总用户数',
      value: '0',
      icon: '&#xe7b9;',
      color: '#67C23A'
    },
    {
      key: 'pendingScores',
      label: '待审核谱曲',
      value: '0',
      icon: '&#xe715;',
      color: '#E6A23C'
    },
    {
      key: 'activeUsers',
      label: '活跃用户',
      value: '0',
      icon: '&#xe816;',
      color: '#F56C6C'
    }
  ])

  const latestScores = ref([])
  const latestUsers = ref([])

  // 获取统计数据
  const fetchStats = async () => {
    try {
      // 获取谱曲统计
      const scoresResponse = await ScoreService.getScores({ limit: 1 })
      stats.value[0].value = scoresResponse.meta.total.toString()

      // 获取待审核谱曲
      const pendingResponse = await ScoreService.getScores({ status: 'pending', limit: 1 })
      stats.value[2].value = pendingResponse.meta.total.toString()

      // 获取用户统计
      const usersResponse = await AdminUserService.getUsers({ limit: 1 })
      stats.value[1].value = usersResponse.total.toString()

      // 获取活跃用户（这里简化为总用户数）
      stats.value[3].value = usersResponse.total.toString()
    } catch (error) {
      console.error('获取统计数据失败:', error)
      // 设置默认值
      stats.value[0].value = '0'
      stats.value[1].value = '0'
      stats.value[2].value = '0'
      stats.value[3].value = '0'
    }
  }

  // 获取最新数据
  const fetchLatestData = async () => {
    try {
      // 获取最新谱曲
      const scoresResponse = await ScoreService.getScores({
        limit: 5,
        sort_by: 'created_at',
        order: 'desc'
      })
      latestScores.value = scoresResponse.data

      // 获取最新用户
      const usersResponse = await AdminUserService.getUsers({ limit: 5 })
      latestUsers.value = usersResponse.items
    } catch (error) {
      console.error('获取最新数据失败:', error)
    }
  }

  // 初始化图表
  const initCharts = async () => {
    await nextTick()

    // 谱曲上传趋势图
    if (uploadTrendChart.value) {
      const chart = echarts.init(uploadTrendChart.value)
      chart.setOption({
        title: { text: '' },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [120, 200, 150, 80, 70, 110],
            type: 'line',
            smooth: true,
            itemStyle: { color: '#409EFF' }
          }
        ]
      })
    }

    // 用户注册趋势图
    if (userTrendChart.value) {
      const chart = echarts.init(userTrendChart.value)
      chart.setOption({
        title: { text: '' },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: ['1月', '2月', '3月', '4月', '5月', '6月']
        },
        yAxis: { type: 'value' },
        series: [
          {
            data: [50, 80, 60, 120, 90, 100],
            type: 'bar',
            itemStyle: { color: '#67C23A' }
          }
        ]
      })
    }

    // 分类分布饼图
    if (categoryChart.value) {
      const chart = echarts.init(categoryChart.value)
      chart.setOption({
        title: { text: '' },
        tooltip: { trigger: 'item' },
        series: [
          {
            type: 'pie',
            radius: '60%',
            data: [
              { value: 35, name: '流行音乐' },
              { value: 25, name: '古典音乐' },
              { value: 20, name: '民族音乐' },
              { value: 20, name: '其他' }
            ]
          }
        ]
      })
    }

    // 用户角色分布
    if (roleChart.value) {
      const chart = echarts.init(roleChart.value)
      chart.setOption({
        title: { text: '' },
        tooltip: { trigger: 'item' },
        series: [
          {
            type: 'pie',
            radius: '60%',
            data: [
              { value: 80, name: '普通用户' },
              { value: 15, name: '代理' },
              { value: 5, name: '管理员' }
            ]
          }
        ]
      })
    }

    // 谱曲状态分布
    if (statusChart.value) {
      const chart = echarts.init(statusChart.value)
      chart.setOption({
        title: { text: '' },
        tooltip: { trigger: 'item' },
        series: [
          {
            type: 'pie',
            radius: '60%',
            data: [
              { value: 70, name: '已通过' },
              { value: 20, name: '待审核' },
              { value: 8, name: '已拒绝' },
              { value: 2, name: '草稿' }
            ]
          }
        ]
      })
    }
  }

  // 工具函数
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const getRoleLabel = (role: string) => {
    const labels: Record<string, string> = {
      admin: '管理员',
      reseller: '代理',
      user: '用户'
    }
    return labels[role] || role
  }

  const getRoleTagType = (role: string) => {
    const types: Record<string, string> = {
      admin: 'danger',
      reseller: 'warning',
      user: 'primary'
    }
    return types[role] || ''
  }

  // 生命周期
  onMounted(async () => {
    await fetchStats()
    await fetchLatestData()
    await initCharts()
  })
</script>

<style scoped>
  .dashboard-overview {
    padding: 20px;
  }

  .stats-cards {
    margin-bottom: 20px;
  }

  .stat-card {
    height: 100px;
  }

  .stat-content {
    display: flex;
    align-items: center;
    height: 100%;
  }

  .stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    margin-right: 15px;
    border-radius: 50%;
  }

  .stat-icon i {
    font-size: 24px;
    color: white;
  }

  .stat-info {
    flex: 1;
  }

  .stat-value {
    margin-bottom: 5px;
    font-size: 24px;
    font-weight: bold;
    color: #303133;
  }

  .stat-label {
    font-size: 14px;
    color: #909399;
  }

  .charts-section,
  .latest-section {
    margin-bottom: 20px;
  }
</style>
