<template>
  <div class="reseller-license-keys">
    <div class="page-header">
      <h2>卡密管理</h2>
      <el-button type="primary" @click="showRequestDialog = true">
        <el-icon><Plus /></el-icon>
        申请卡密
      </el-button>
    </div>

    <!-- 库存统计 -->
    <div class="inventory-stats">
      <el-row :gutter="20">
        <el-col :span="6" v-for="(stat, index) in inventoryStats" :key="index">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ stat.count }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
            <el-icon class="stat-icon" :class="stat.iconClass">
              <component :is="stat.icon" />
            </el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="卡密类型">
          <el-select v-model="searchForm.license_type_id" placeholder="选择类型" clearable>
            <el-option
              v-for="type in licenseTypes"
              :key="type.id"
              :label="type.name"
              :value="type.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="未使用" value="unused" />
            <el-option label="已使用" value="used" />
            <el-option label="已过期" value="expired" />
            <el-option label="已禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadLicenseKeys">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 卡密列表 -->
    <el-card>
      <el-table :data="licenseKeys" v-loading="loading" stripe>
        <el-table-column prop="key_string" label="卡密" width="200">
          <template #default="{ row }">
            <div class="key-string">
              <span>{{ row.key_string }}</span>
              <el-button
                size="small"
                text
                @click="copyToClipboard(row.key_string)"
                style="margin-left: 8px"
              >
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="类型" width="120">
          <template #default="{ row }">
            {{ row.license_type?.name || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="销售价格" width="120">
          <template #default="{ row }">
            {{ row.sale_price ? `¥${row.sale_price}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="客户信息" width="150">
          <template #default="{ row }">
            {{ row.customer_info || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="激活时间" width="180">
          <template #default="{ row }">
            {{ row.activated_at ? formatDate(row.activated_at) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="过期时间" width="180">
          <template #default="{ row }">
            {{ row.expires_at ? formatDate(row.expires_at) : '永久' }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewLicenseKey(row)">详情</el-button>
            <el-button 
              v-if="row.status === 'unused'"
              size="small" 
              type="success" 
              @click="markAsSold(row)"
            >
              标记售出
            </el-button>
            <el-button 
              v-if="row.status === 'unused'"
              size="small" 
              type="warning" 
              @click="editPrice(row)"
            >
              设置价格
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadLicenseKeys"
          @current-change="loadLicenseKeys"
        />
      </div>
    </el-card>

    <!-- 申请卡密对话框 -->
    <el-dialog v-model="showRequestDialog" title="申请卡密" width="500px">
      <el-form
        ref="requestFormRef"
        :model="requestForm"
        :rules="requestFormRules"
        label-width="100px"
      >
        <el-form-item label="卡密类型" prop="license_type_id">
          <el-select v-model="requestForm.license_type_id" style="width: 100%">
            <el-option
              v-for="type in licenseTypes"
              :key="type.id"
              :label="`${type.name} (¥${type.price || 0})`"
              :value="type.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="申请数量" prop="quantity">
          <el-input-number
            v-model="requestForm.quantity"
            :min="1"
            :max="100"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="申请备注">
          <el-input
            v-model="requestForm.notes"
            type="textarea"
            placeholder="可选备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showRequestDialog = false">取消</el-button>
        <el-button type="primary" @click="submitRequest" :loading="requesting">
          提交申请
        </el-button>
      </template>
    </el-dialog>

    <!-- 标记售出对话框 -->
    <el-dialog v-model="showSoldDialog" title="标记售出" width="500px">
      <el-form
        ref="soldFormRef"
        :model="soldForm"
        :rules="soldFormRules"
        label-width="100px"
      >
        <el-form-item label="卡密">
          <span>{{ selectedLicenseKey?.key_string }}</span>
        </el-form-item>
        <el-form-item label="销售价格" prop="sale_price">
          <el-input-number
            v-model="soldForm.sale_price"
            :min="0"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="客户信息">
          <el-input
            v-model="soldForm.customer_info"
            placeholder="客户联系方式或备注"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="soldForm.notes"
            type="textarea"
            placeholder="销售备注"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showSoldDialog = false">取消</el-button>
        <el-button type="primary" @click="submitSold" :loading="marking">
          确认售出
        </el-button>
      </template>
    </el-dialog>

    <!-- 卡密详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="卡密详情" width="600px">
      <div v-if="selectedLicenseKey" class="license-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="卡密">{{ selectedLicenseKey.key_string }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ selectedLicenseKey.license_type?.name }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedLicenseKey.status)">
              {{ getStatusText(selectedLicenseKey.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="销售价格">
            {{ selectedLicenseKey.sale_price ? `¥${selectedLicenseKey.sale_price}` : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="客户信息" :span="2">
            {{ selectedLicenseKey.customer_info || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="激活时间">
            {{ selectedLicenseKey.activated_at ? formatDate(selectedLicenseKey.activated_at) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="过期时间">
            {{ selectedLicenseKey.expires_at ? formatDate(selectedLicenseKey.expires_at) : '永久' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ formatDate(selectedLicenseKey.created_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Plus, DocumentCopy, Key, ShoppingCart, Clock, CheckCircle } from '@element-plus/icons-vue'
import { ResellerLicenseService } from '@/api/resellerApi'
import { formatDate } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const requesting = ref(false)
const marking = ref(false)
const licenseKeys = ref<any[]>([])
const licenseTypes = ref<any[]>([])
const showRequestDialog = ref(false)
const showSoldDialog = ref(false)
const showDetailDialog = ref(false)
const selectedLicenseKey = ref<any>(null)

// 搜索表单
const searchForm = reactive({
  license_type_id: undefined as number | undefined,
  status: ''
})

const dateRange = ref<[Date, Date] | null>(null)

// 分页
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 申请表单
const requestForm = reactive({
  license_type_id: undefined as number | undefined,
  quantity: 10,
  notes: ''
})

const requestFormRef = ref<FormInstance>()

// 售出表单
const soldForm = reactive({
  sale_price: 0,
  customer_info: '',
  notes: ''
})

const soldFormRef = ref<FormInstance>()

// 表单验证规则
const requestFormRules: FormRules = {
  license_type_id: [
    { required: true, message: '请选择卡密类型', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入申请数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '数量必须在 1-100 之间', trigger: 'blur' }
  ]
}

const soldFormRules: FormRules = {
  sale_price: [
    { required: true, message: '请输入销售价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能为负数', trigger: 'blur' }
  ]
}

// 库存统计
const inventoryStats = computed(() => {
  const stats = licenseKeys.value.reduce((acc, key) => {
    acc[key.status] = (acc[key.status] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return [
    {
      label: '未使用',
      count: stats.unused || 0,
      icon: Key,
      iconClass: 'unused'
    },
    {
      label: '已售出',
      count: stats.used || 0,
      icon: ShoppingCart,
      iconClass: 'used'
    },
    {
      label: '已过期',
      count: stats.expired || 0,
      icon: Clock,
      iconClass: 'expired'
    },
    {
      label: '总计',
      count: licenseKeys.value.length,
      icon: CheckCircle,
      iconClass: 'total'
    }
  ]
})

// 方法
const loadLicenseKeys = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.limit,
      limit: pagination.limit,
      license_type_id: searchForm.license_type_id,
      status: searchForm.status || undefined,
      start_date: dateRange.value?.[0]?.toISOString(),
      end_date: dateRange.value?.[1]?.toISOString()
    }
    
    const response = await ResellerLicenseService.getLicenseKeySales(params)
    licenseKeys.value = response.items
    pagination.total = response.total
  } catch (error: any) {
    ElMessage.error(error.message || '加载卡密列表失败')
  } finally {
    loading.value = false
  }
}

const loadLicenseTypes = async () => {
  try {
    const types = await ResellerLicenseService.getAvailableLicenseTypes()
    licenseTypes.value = types
  } catch (error: any) {
    ElMessage.error(error.message || '加载卡密类型失败')
  }
}

const resetSearch = () => {
  searchForm.license_type_id = undefined
  searchForm.status = ''
  dateRange.value = null
  pagination.page = 1
  loadLicenseKeys()
}

const submitRequest = async () => {
  if (!requestFormRef.value) return
  
  try {
    await requestFormRef.value.validate()
    requesting.value = true
    
    await ResellerLicenseService.requestLicenseKeys({
      license_type_id: requestForm.license_type_id!,
      quantity: requestForm.quantity,
      notes: requestForm.notes || undefined
    })
    
    ElMessage.success('申请提交成功，请等待管理员审核')
    showRequestDialog.value = false
    resetRequestForm()
  } catch (error: any) {
    ElMessage.error(error.message || '申请失败')
  } finally {
    requesting.value = false
  }
}

const markAsSold = (licenseKey: any) => {
  selectedLicenseKey.value = licenseKey
  soldForm.sale_price = licenseKey.license_type?.price || 0
  showSoldDialog.value = true
}

const submitSold = async () => {
  if (!soldFormRef.value || !selectedLicenseKey.value) return
  
  try {
    await soldFormRef.value.validate()
    marking.value = true
    
    await ResellerLicenseService.markLicenseKeySold(selectedLicenseKey.value.id, {
      sale_price: soldForm.sale_price,
      customer_info: soldForm.customer_info || undefined,
      notes: soldForm.notes || undefined
    })
    
    ElMessage.success('标记售出成功')
    showSoldDialog.value = false
    resetSoldForm()
    loadLicenseKeys()
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  } finally {
    marking.value = false
  }
}

const viewLicenseKey = (licenseKey: any) => {
  selectedLicenseKey.value = licenseKey
  showDetailDialog.value = true
}

const editPrice = (licenseKey: any) => {
  // TODO: 实现价格编辑功能
  ElMessage.info('价格编辑功能开发中')
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const resetRequestForm = () => {
  Object.assign(requestForm, {
    license_type_id: undefined,
    quantity: 10,
    notes: ''
  })
  requestFormRef.value?.clearValidate()
}

const resetSoldForm = () => {
  Object.assign(soldForm, {
    sale_price: 0,
    customer_info: '',
    notes: ''
  })
  soldFormRef.value?.clearValidate()
}

// 辅助方法
const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    unused: 'info',
    used: 'success',
    expired: 'warning',
    disabled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    unused: '未使用',
    used: '已使用',
    expired: '已过期',
    disabled: '已禁用'
  }
  return texts[status] || status
}

// 监听对话框关闭
watch(() => showRequestDialog.value, (newVal) => {
  if (!newVal) {
    resetRequestForm()
  }
})

watch(() => showSoldDialog.value, (newVal) => {
  if (!newVal) {
    resetSoldForm()
    selectedLicenseKey.value = null
  }
})

// 初始化
onMounted(() => {
  loadLicenseTypes()
  loadLicenseKeys()
})
</script>

<style lang="scss" scoped>
.reseller-license-keys {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }
  
  .inventory-stats {
    margin-bottom: 20px;
    
    .stat-card {
      position: relative;
      overflow: hidden;
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: var(--el-text-color-primary);
          margin-bottom: 8px;
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-secondary);
        }
      }
      
      .stat-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 40px;
        opacity: 0.3;
        
        &.unused { color: #909399; }
        &.used { color: #67c23a; }
        &.expired { color: #e6a23c; }
        &.total { color: #409eff; }
      }
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  .key-string {
    display: flex;
    align-items: center;
    font-family: monospace;
  }
  
  .license-detail {
    .el-descriptions {
      margin-top: 20px;
    }
  }
}
</style>
