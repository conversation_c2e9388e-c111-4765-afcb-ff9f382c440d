<template>
  <div class="reseller-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>代理列表</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增代理
          </el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="搜索">
          <el-input
            v-model="searchForm.search"
            placeholder="请输入用户名或邮箱"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" min-width="120" />
        <el-table-column prop="nickname" label="昵称" min-width="120">
          <template #default="{ row }">
            {{ row.nickname || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="email" label="邮箱" min-width="180">
          <template #default="{ row }">
            {{ row.email || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="role" label="角色" width="100">
          <template #default="{ row }">
            <el-tag :type="getRoleTagType(row.role)">
              {{ getRoleText(row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-dropdown @command="(command) => handleAction(command, row)">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="resetPassword">重置密码</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除代理</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchData"
          @current-change="fetchData"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="dialogVisible" title="代理详情" width="600px">
      <div v-if="currentReseller" class="reseller-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ currentReseller.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ currentReseller.username }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{ currentReseller.nickname || '-' }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ currentReseller.email || '-' }}</el-descriptions-item>
          <el-descriptions-item label="角色">
            <el-tag :type="getRoleTagType(currentReseller.role)">
              {{ getRoleText(currentReseller.role) }}
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { Plus, ArrowDown } from '@element-plus/icons-vue'
  import { ResellerService } from '@/api/adminApi'
  import type { UserBaseInfoForReseller } from '@/api/model/categoryModel'

  // 响应式数据
  const loading = ref(false)
  const tableData = ref<UserBaseInfoForReseller[]>([])
  const selectedRows = ref<UserBaseInfoForReseller[]>([])
  const dialogVisible = ref(false)
  const currentReseller = ref<UserBaseInfoForReseller | null>(null)

  // 搜索表单
  const searchForm = reactive({
    search: ''
  })

  // 分页数据
  const pagination = reactive({
    page: 1,
    limit: 20,
    total: 0
  })

  // 获取数据
  const fetchData = async () => {
    loading.value = true
    try {
      const params = {
        skip: (pagination.page - 1) * pagination.limit,
        limit: pagination.limit,
        ...searchForm
      }

      const response = await ResellerService.getResellers(params)
      tableData.value = response.items
      pagination.total = response.total
    } catch {
      ElMessage.error('获取数据失败')
    } finally {
      loading.value = false
    }
  }

  // 事件处理
  const handleSearch = () => {
    pagination.page = 1
    fetchData()
  }

  const handleReset = () => {
    Object.assign(searchForm, {
      search: ''
    })
    handleSearch()
  }

  const handleSelectionChange = (selection: UserBaseInfoForReseller[]) => {
    selectedRows.value = selection
  }

  const handleAdd = () => {
    // 跳转到新增页面
    console.log('新增代理')
  }

  const handleView = (row: UserBaseInfoForReseller) => {
    currentReseller.value = row
    dialogVisible.value = true
  }

  const handleEdit = (row: UserBaseInfoForReseller) => {
    // 跳转到编辑页面
    console.log('编辑代理', row)
  }

  const handleAction = async (command: string, row: UserBaseInfoForReseller) => {
    switch (command) {
      case 'resetPassword':
        await handleResetPassword(row)
        break
      case 'delete':
        await handleDeleteReseller(row)
        break
    }
  }

  const handleResetPassword = async (row: UserBaseInfoForReseller) => {
    try {
      await ElMessageBox.confirm('确定要重置这个代理的密码吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      // TODO: 调用重置密码API
      ElMessage.success('密码重置成功')
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('密码重置失败')
      }
    }
  }

  const handleDeleteReseller = async (row: UserBaseInfoForReseller) => {
    try {
      await ElMessageBox.confirm('确定要删除这个代理吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      // TODO: 调用删除API
      ElMessage.success('删除成功')
      fetchData()
    } catch (error) {
      if (error !== 'cancel') {
        ElMessage.error('删除失败')
      }
    }
  }

  // 工具函数
  const getRoleTagType = (role: string) => {
    const roleMap: Record<string, string> = {
      admin: 'danger',
      reseller: 'warning',
      user: 'info'
    }
    return roleMap[role] || 'info'
  }

  const getRoleText = (role: string) => {
    const roleMap: Record<string, string> = {
      admin: '管理员',
      reseller: '代理商',
      user: '普通用户'
    }
    return roleMap[role] || role
  }

  // 生命周期
  onMounted(() => {
    fetchData()
  })
</script>

<style lang="scss" scoped>
  .reseller-list {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .search-form {
      margin-bottom: 20px;
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }

    .reseller-detail {
      padding: 20px 0;
    }
  }
</style>
