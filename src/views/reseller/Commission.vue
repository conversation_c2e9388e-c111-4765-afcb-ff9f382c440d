<template>
  <div class="reseller-commission">
    <div class="page-header">
      <h2>佣金记录</h2>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ totalCommission.toFixed(2) }}</div>
              <div class="stat-label">总佣金</div>
            </div>
            <el-icon class="stat-icon total"><Coin /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ monthlyCommission.toFixed(2) }}</div>
              <div class="stat-label">本月佣金</div>
            </div>
            <el-icon class="stat-icon monthly"><TrendCharts /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ pendingCommission.toFixed(2) }}</div>
              <div class="stat-label">待结算佣金</div>
            </div>
            <el-icon class="stat-icon pending"><Clock /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ commissionRate }}%</div>
              <div class="stat-label">佣金率</div>
            </div>
            <el-icon class="stat-icon rate"><Percentage /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="待结算" value="pending" />
            <el-option label="已结算" value="settled" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadCommissionRecords">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="exportRecords">导出记录</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 佣金记录列表 -->
    <el-card>
      <el-table :data="commissionRecords" v-loading="loading" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="卡密" width="150">
          <template #default="{ row }">
            <span class="key-string">{{ row.license_key?.key_string || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="卡密类型" width="120">
          <template #default="{ row }">
            {{ row.license_key?.license_type?.name || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="销售金额" width="120">
          <template #default="{ row }">
            ¥{{ row.sale_amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="佣金率" width="100">
          <template #default="{ row }">
            {{ (row.commission_rate * 100).toFixed(1) }}%
          </template>
        </el-table-column>
        <el-table-column label="佣金金额" width="120">
          <template #default="{ row }">
            <span class="commission-amount">¥{{ row.commission_amount.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="结算时间" width="180">
          <template #default="{ row }">
            {{ row.settled_at ? formatDate(row.settled_at) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewCommission(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadCommissionRecords"
          @current-change="loadCommissionRecords"
        />
      </div>
    </el-card>

    <!-- 佣金详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="佣金详情" width="600px">
      <div v-if="selectedCommission" class="commission-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ selectedCommission.id }}</el-descriptions-item>
          <el-descriptions-item label="卡密">{{ selectedCommission.license_key?.key_string }}</el-descriptions-item>
          <el-descriptions-item label="卡密类型">{{ selectedCommission.license_key?.license_type?.name }}</el-descriptions-item>
          <el-descriptions-item label="销售金额">¥{{ selectedCommission.sale_amount.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="佣金率">{{ (selectedCommission.commission_rate * 100).toFixed(1) }}%</el-descriptions-item>
          <el-descriptions-item label="佣金金额">¥{{ selectedCommission.commission_amount.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedCommission.status)">
              {{ getStatusText(selectedCommission.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedCommission.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="结算时间" :span="2">
            {{ selectedCommission.settled_at ? formatDate(selectedCommission.settled_at) : '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Coin, TrendCharts, Clock, Percentage } from '@element-plus/icons-vue'
import { ResellerService } from '@/api/resellerApi'
import { formatDate } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const commissionRecords = ref<any[]>([])
const showDetailDialog = ref(false)
const selectedCommission = ref<any>(null)
const commissionRate = ref(0)

// 搜索表单
const searchForm = reactive({
  status: ''
})

const dateRange = ref<[Date, Date] | null>(null)

// 分页
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 统计数据
const totalCommission = computed(() => {
  return commissionRecords.value.reduce((sum, record) => {
    return sum + (record.status === 'settled' ? record.commission_amount : 0)
  }, 0)
})

const monthlyCommission = computed(() => {
  const now = new Date()
  const currentMonth = now.getMonth()
  const currentYear = now.getFullYear()
  
  return commissionRecords.value.reduce((sum, record) => {
    const recordDate = new Date(record.created_at)
    if (recordDate.getMonth() === currentMonth && 
        recordDate.getFullYear() === currentYear &&
        record.status === 'settled') {
      return sum + record.commission_amount
    }
    return sum
  }, 0)
})

const pendingCommission = computed(() => {
  return commissionRecords.value.reduce((sum, record) => {
    return sum + (record.status === 'pending' ? record.commission_amount : 0)
  }, 0)
})

// 方法
const loadCommissionRecords = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.limit,
      limit: pagination.limit,
      status: searchForm.status || undefined,
      start_date: dateRange.value?.[0]?.toISOString(),
      end_date: dateRange.value?.[1]?.toISOString()
    }
    
    const response = await ResellerService.getCommissionRecords(params)
    commissionRecords.value = response.items
    pagination.total = response.total
  } catch (error: any) {
    ElMessage.error(error.message || '加载佣金记录失败')
  } finally {
    loading.value = false
  }
}

const loadResellerInfo = async () => {
  try {
    const info = await ResellerService.getResellerInfo()
    commissionRate.value = (info.commission_rate * 100).toFixed(1)
  } catch (error: any) {
    ElMessage.error(error.message || '加载代理商信息失败')
  }
}

const resetSearch = () => {
  searchForm.status = ''
  dateRange.value = null
  pagination.page = 1
  loadCommissionRecords()
}

const viewCommission = (commission: any) => {
  selectedCommission.value = commission
  showDetailDialog.value = true
}

const exportRecords = async () => {
  try {
    ElMessage.info('导出功能开发中')
    // TODO: 实现导出功能
  } catch (error: any) {
    ElMessage.error(error.message || '导出失败')
  }
}

// 辅助方法
const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    settled: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待结算',
    settled: '已结算',
    cancelled: '已取消'
  }
  return texts[status] || status
}

// 初始化
onMounted(() => {
  loadResellerInfo()
  loadCommissionRecords()
})
</script>

<style lang="scss" scoped>
.reseller-commission {
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }
  
  .stats-cards {
    margin-bottom: 20px;
    
    .stat-card {
      position: relative;
      overflow: hidden;
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: var(--el-text-color-primary);
          margin-bottom: 8px;
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-secondary);
        }
      }
      
      .stat-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 40px;
        opacity: 0.3;
        
        &.total { color: #67c23a; }
        &.monthly { color: #409eff; }
        &.pending { color: #e6a23c; }
        &.rate { color: #f56c6c; }
      }
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  .key-string {
    font-family: monospace;
    font-size: 12px;
  }
  
  .commission-amount {
    font-weight: bold;
    color: var(--el-color-success);
  }
  
  .commission-detail {
    .el-descriptions {
      margin-top: 20px;
    }
  }
}
</style>
