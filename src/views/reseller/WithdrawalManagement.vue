<template>
  <div class="withdrawal-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>提现管理</span>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="待处理" value="pending" />
            <el-option label="已批准" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>
        <el-form-item label="代理用户ID">
          <el-input
            v-model="searchForm.reseller_user_id"
            placeholder="请输入代理用户ID"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="reseller_username" label="代理用户" min-width="120" />
        <el-table-column prop="amount" label="提现金额" width="120">
          <template #default="{ row }">
            ¥{{ row.amount }}
          </template>
        </el-table-column>
        <el-table-column prop="payment_method" label="提现方式" width="120" />
        <el-table-column prop="payment_account" label="提现账户" min-width="180" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="申请时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="processed_at" label="处理时间" width="180">
          <template #default="{ row }">
            {{ row.processed_at ? formatDateTime(row.processed_at) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button
              v-if="row.status === 'pending'"
              type="success"
              size="small"
              @click="handleApprove(row)"
            >
              批准
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              type="danger"
              size="small"
              @click="handleReject(row)"
            >
              拒绝
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchData"
          @current-change="fetchData"
        />
      </div>
    </el-card>

    <!-- 查看详情对话框 -->
    <el-dialog v-model="dialogVisible" title="提现详情" width="600px">
      <div v-if="currentWithdrawal" class="withdrawal-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ currentWithdrawal.id }}</el-descriptions-item>
          <el-descriptions-item label="代理用户">{{ currentWithdrawal.reseller_username }}</el-descriptions-item>
          <el-descriptions-item label="提现金额">¥{{ currentWithdrawal.amount }}</el-descriptions-item>
          <el-descriptions-item label="提现方式">{{ currentWithdrawal.payment_method }}</el-descriptions-item>
          <el-descriptions-item label="提现账户" :span="2">{{ currentWithdrawal.payment_account }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(currentWithdrawal.status)">
              {{ getStatusText(currentWithdrawal.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDateTime(currentWithdrawal.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="处理时间">
            {{ currentWithdrawal.processed_at ? formatDateTime(currentWithdrawal.processed_at) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="用户备注" :span="2">{{ currentWithdrawal.remarks || '-' }}</el-descriptions-item>
          <el-descriptions-item label="管理员备注" :span="2">{{ currentWithdrawal.admin_notes || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 处理提现对话框 -->
    <el-dialog v-model="processDialogVisible" :title="processType === 'approve' ? '批准提现' : '拒绝提现'" width="500px">
      <el-form :model="processForm" label-width="100px">
        <el-form-item label="处理备注">
          <el-input
            v-model="processForm.remarks"
            type="textarea"
            :rows="4"
            :placeholder="processType === 'approve' ? '请输入批准备注（可选）' : '请输入拒绝原因'"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="processDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleProcessConfirm" :loading="processLoading">
          确定{{ processType === 'approve' ? '批准' : '拒绝' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ResellerService } from '@/api/adminApi'
  import type { WithdrawalRecordResponse } from '@/api/model/categoryModel'

  // 响应式数据
  const loading = ref(false)
  const tableData = ref<WithdrawalRecordResponse[]>([])
  const selectedRows = ref<WithdrawalRecordResponse[]>([])
  const dialogVisible = ref(false)
  const currentWithdrawal = ref<WithdrawalRecordResponse | null>(null)
  const processDialogVisible = ref(false)
  const processType = ref<'approve' | 'reject'>('approve')
  const processLoading = ref(false)

  // 搜索表单
  const searchForm = reactive({
    status: '',
    reseller_user_id: ''
  })

  // 处理表单
  const processForm = reactive({
    remarks: ''
  })

  // 分页数据
  const pagination = reactive({
    page: 1,
    limit: 20,
    total: 0
  })

  // 获取数据
  const fetchData = async () => {
    loading.value = true
    try {
      const params = {
        skip: (pagination.page - 1) * pagination.limit,
        limit: pagination.limit,
        ...searchForm
      }

      const response = await ResellerService.getWithdrawalRecords(params)
      tableData.value = response.items
      pagination.total = response.total
    } catch {
      ElMessage.error('获取数据失败')
    } finally {
      loading.value = false
    }
  }

  // 事件处理
  const handleSearch = () => {
    pagination.page = 1
    fetchData()
  }

  const handleReset = () => {
    Object.assign(searchForm, {
      status: '',
      reseller_user_id: ''
    })
    handleSearch()
  }

  const handleSelectionChange = (selection: WithdrawalRecordResponse[]) => {
    selectedRows.value = selection
  }

  const handleView = (row: WithdrawalRecordResponse) => {
    currentWithdrawal.value = row
    dialogVisible.value = true
  }

  const handleApprove = (row: WithdrawalRecordResponse) => {
    currentWithdrawal.value = row
    processType.value = 'approve'
    processForm.remarks = ''
    processDialogVisible.value = true
  }

  const handleReject = (row: WithdrawalRecordResponse) => {
    currentWithdrawal.value = row
    processType.value = 'reject'
    processForm.remarks = ''
    processDialogVisible.value = true
  }

  const handleProcessConfirm = async () => {
    if (!currentWithdrawal.value) return

    processLoading.value = true
    try {
      const status = processType.value === 'approve' ? 'approved' : 'rejected'
      await ResellerService.processWithdrawal(currentWithdrawal.value.id, {
        status,
        remarks: processForm.remarks
      })

      ElMessage.success(`${processType.value === 'approve' ? '批准' : '拒绝'}成功`)
      processDialogVisible.value = false
      fetchData()
    } catch {
      ElMessage.error(`${processType.value === 'approve' ? '批准' : '拒绝'}失败`)
    } finally {
      processLoading.value = false
    }
  }

  // 工具函数
  const getStatusTagType = (status: string) => {
    const statusMap: Record<string, string> = {
      pending: 'warning',
      approved: 'success',
      rejected: 'danger',
      completed: 'info',
      failed: 'danger'
    }
    return statusMap[status] || 'info'
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      pending: '待处理',
      approved: '已批准',
      rejected: '已拒绝',
      completed: '已完成',
      failed: '失败'
    }
    return statusMap[status] || status
  }

  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 生命周期
  onMounted(() => {
    fetchData()
  })
</script>

<style lang="scss" scoped>
  .withdrawal-management {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .search-form {
      margin-bottom: 20px;
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }

    .withdrawal-detail {
      padding: 20px 0;
    }
  }
</style>
