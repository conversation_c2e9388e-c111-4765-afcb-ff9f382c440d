<template>
  <div class="reseller-dashboard">
    <div class="page-header">
      <h2>代理商控制台</h2>
      <div class="user-info">
        <span>欢迎，{{ userInfo?.username }}</span>
        <el-tag :type="getStatusTagType(userInfo?.status)">
          {{ getStatusText(userInfo?.status) }}
        </el-tag>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ stats.totalSales || 0 }}</div>
              <div class="stat-label">总销售量</div>
            </div>
            <el-icon class="stat-icon sales"><ShoppingCart /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ (stats.totalRevenue || 0).toFixed(2) }}</div>
              <div class="stat-label">总收入</div>
            </div>
            <el-icon class="stat-icon revenue"><Money /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ (stats.totalCommission || 0).toFixed(2) }}</div>
              <div class="stat-label">总佣金</div>
            </div>
            <el-icon class="stat-icon commission"><Coin /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ (stats.availableBalance || 0).toFixed(2) }}</div>
              <div class="stat-label">可提现余额</div>
            </div>
            <el-icon class="stat-icon balance"><Wallet /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快捷操作 -->
    <el-card class="quick-actions" style="margin-bottom: 20px">
      <template #header>
        <span>快捷操作</span>
      </template>
      <div class="actions-grid">
        <el-button type="primary" @click="$router.push('/reseller/license-keys')">
          <el-icon><Key /></el-icon>
          管理卡密
        </el-button>
        <el-button type="success" @click="showWithdrawalDialog = true">
          <el-icon><Money /></el-icon>
          申请提现
        </el-button>
        <el-button type="info" @click="$router.push('/reseller/sales')">
          <el-icon><DataAnalysis /></el-icon>
          销售统计
        </el-button>
        <el-button type="warning" @click="$router.push('/reseller/commission')">
          <el-icon><Coin /></el-icon>
          佣金记录
        </el-button>
      </div>
    </el-card>

    <!-- 最近销售 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>最近销售</span>
          </template>
          <el-table :data="recentSales" size="small" max-height="300">
            <el-table-column prop="license_key" label="卡密" width="150">
              <template #default="{ row }">
                {{ row.license_key?.substring(0, 8) }}...
              </template>
            </el-table-column>
            <el-table-column label="类型" width="100">
              <template #default="{ row }">
                {{ row.license_type?.name }}
              </template>
            </el-table-column>
            <el-table-column label="金额" width="80">
              <template #default="{ row }">
                ¥{{ row.sale_amount }}
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="时间" width="120">
              <template #default="{ row }">
                {{ formatDate(row.created_at, 'MM-DD HH:mm') }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>待处理提现</span>
          </template>
          <el-table :data="pendingWithdrawals" size="small" max-height="300">
            <el-table-column label="金额" width="100">
              <template #default="{ row }">
                ¥{{ typeof row.amount === 'number' ? row.amount.toFixed(2) : row.amount }}
              </template>
            </el-table-column>
            <el-table-column prop="payment_method" label="方式" width="80" />
            <el-table-column label="状态" width="80">
              <template #default="{ row }">
                <el-tag size="small" :type="getWithdrawalStatusTagType(row.status)">
                  {{ getWithdrawalStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="申请时间" width="120">
              <template #default="{ row }">
                {{ formatDate(row.created_at, 'MM-DD HH:mm') }}
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 提现申请对话框 -->
    <el-dialog v-model="showWithdrawalDialog" title="申请提现" width="500px">
      <el-form
        ref="withdrawalFormRef"
        :model="withdrawalForm"
        :rules="withdrawalFormRules"
        label-width="100px"
      >
        <el-form-item label="可提现余额">
          <span class="balance-display">¥{{ (stats.availableBalance || 0).toFixed(2) }}</span>
        </el-form-item>
        <el-form-item label="提现金额" prop="amount">
          <el-input-number
            v-model="withdrawalForm.amount"
            :min="1"
            :max="stats.availableBalance || 0"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="提现方式" prop="payment_method">
          <el-select v-model="withdrawalForm.payment_method" style="width: 100%">
            <el-option label="支付宝" value="alipay" />
            <el-option label="微信" value="wechat" />
            <el-option label="银行卡" value="bank" />
          </el-select>
        </el-form-item>
        <el-form-item label="收款账户" prop="payment_account">
          <el-input
            v-model="withdrawalForm.payment_account"
            placeholder="请输入收款账户信息"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="withdrawalForm.remarks"
            type="textarea"
            placeholder="可选备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showWithdrawalDialog = false">取消</el-button>
        <el-button type="primary" @click="submitWithdrawal" :loading="submitting">
          提交申请
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { 
  ShoppingCart, 
  Money, 
  Coin, 
  Wallet, 
  Key, 
  DataAnalysis 
} from '@element-plus/icons-vue'
import { ResellerService } from '@/api/resellerApi'
import { useUserStore } from '@/store/modules/user'
import { formatDate } from '@/utils/date'

// Store
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showWithdrawalDialog = ref(false)
const userInfo = ref<any>(null)
const stats = ref({
  totalSales: 0,
  totalRevenue: 0,
  totalCommission: 0,
  availableBalance: 0
})
const recentSales = ref<any[]>([])
const pendingWithdrawals = ref<any[]>([])

// 提现表单
const withdrawalForm = reactive({
  amount: 0,
  payment_method: '',
  payment_account: '',
  remarks: ''
})

const withdrawalFormRef = ref<FormInstance>()

// 表单验证规则
const withdrawalFormRules: FormRules = {
  amount: [
    { required: true, message: '请输入提现金额', trigger: 'blur' },
    { type: 'number', min: 1, message: '提现金额必须大于0', trigger: 'blur' }
  ],
  payment_method: [
    { required: true, message: '请选择提现方式', trigger: 'change' }
  ],
  payment_account: [
    { required: true, message: '请输入收款账户', trigger: 'blur' }
  ]
}

// 方法
const loadDashboardData = async () => {
  loading.value = true
  try {
    // 加载代理商信息
    const resellerInfo = await ResellerService.getResellerInfo()
    userInfo.value = resellerInfo.user_info

    // 加载统计数据
    const statsData = await ResellerService.getResellerStats()
    stats.value = {
      totalSales: statsData.total_sales || 0,
      totalRevenue: statsData.total_revenue || 0,
      totalCommission: statsData.total_commission || 0,
      availableBalance: statsData.available_balance || 0
    }

    // 加载最近销售
    const salesResponse = await ResellerService.getLicenseKeySales({ limit: 5 })
    recentSales.value = salesResponse.items || []

    // 加载待处理提现
    const withdrawalResponse = await ResellerService.getWithdrawalRecords({ 
      status: 'pending',
      limit: 5 
    })
    pendingWithdrawals.value = withdrawalResponse.items || []

  } catch (error: any) {
    ElMessage.error(error.message || '加载数据失败')
  } finally {
    loading.value = false
  }
}

const submitWithdrawal = async () => {
  if (!withdrawalFormRef.value) return
  
  try {
    await withdrawalFormRef.value.validate()
    submitting.value = true
    
    await ResellerService.createWithdrawalRecord({
      amount: withdrawalForm.amount,
      payment_method: withdrawalForm.payment_method,
      payment_account: withdrawalForm.payment_account,
      remarks: withdrawalForm.remarks || undefined
    })
    
    ElMessage.success('提现申请提交成功')
    showWithdrawalDialog.value = false
    resetWithdrawalForm()
    loadDashboardData() // 重新加载数据
  } catch (error: any) {
    ElMessage.error(error.message || '提交失败')
  } finally {
    submitting.value = false
  }
}

const resetWithdrawalForm = () => {
  Object.assign(withdrawalForm, {
    amount: 0,
    payment_method: '',
    payment_account: '',
    remarks: ''
  })
  withdrawalFormRef.value?.clearValidate()
}

// 辅助方法
const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    active: 'success',
    suspended: 'warning',
    disabled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    active: '活跃',
    suspended: '暂停',
    disabled: '禁用'
  }
  return texts[status] || status
}

const getWithdrawalStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    approved: 'primary',
    rejected: 'danger',
    completed: 'success',
    failed: 'danger'
  }
  return types[status] || 'info'
}

const getWithdrawalStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    approved: '已批准',
    rejected: '已拒绝',
    completed: '已完成',
    failed: '失败'
  }
  return texts[status] || status
}

// 监听对话框关闭
watch(() => showWithdrawalDialog.value, (newVal) => {
  if (!newVal) {
    resetWithdrawalForm()
  }
})

// 初始化
onMounted(() => {
  loadDashboardData()
})
</script>

<style lang="scss" scoped>
.reseller-dashboard {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }
  
  .stats-cards {
    margin-bottom: 20px;
    
    .stat-card {
      position: relative;
      overflow: hidden;
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: var(--el-text-color-primary);
          margin-bottom: 8px;
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-secondary);
        }
      }
      
      .stat-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 40px;
        opacity: 0.3;
        
        &.sales { color: #409eff; }
        &.revenue { color: #67c23a; }
        &.commission { color: #e6a23c; }
        &.balance { color: #f56c6c; }
      }
    }
  }
  
  .quick-actions {
    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 15px;
    }
  }
  
  .balance-display {
    font-size: 18px;
    font-weight: bold;
    color: var(--el-color-success);
  }
}
</style>
