<template>
  <div class="reseller-sales">
    <div class="page-header">
      <h2>销售统计</h2>
      <div class="header-actions">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="loadSalesStats"
        />
        <el-button type="primary" @click="loadSalesStats">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ salesStats.total_sales || 0 }}</div>
              <div class="stat-label">总销售量</div>
            </div>
            <el-icon class="stat-icon sales"><ShoppingCart /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ (salesStats.total_revenue || 0).toFixed(2) }}</div>
              <div class="stat-label">总销售额</div>
            </div>
            <el-icon class="stat-icon revenue"><Money /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ (salesStats.total_commission || 0).toFixed(2) }}</div>
              <div class="stat-label">总佣金</div>
            </div>
            <el-icon class="stat-icon commission"><Coin /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ (salesStats.avg_order_value || 0).toFixed(2) }}</div>
              <div class="stat-label">平均订单价值</div>
            </div>
            <el-icon class="stat-icon avg"><TrendCharts /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>销售趋势</span>
          </template>
          <div ref="salesTrendChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>卡密类型分布</span>
          </template>
          <div ref="licenseTypeChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 销售记录 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <div class="card-header">
          <span>销售记录</span>
          <el-button type="primary" @click="exportSalesData">导出数据</el-button>
        </div>
      </template>
      
      <!-- 搜索筛选 -->
      <el-form :model="searchForm" inline style="margin-bottom: 20px">
        <el-form-item label="卡密类型">
          <el-select v-model="searchForm.license_type_id" placeholder="选择类型" clearable>
            <el-option
              v-for="type in licenseTypes"
              :key="type.id"
              :label="type.name"
              :value="type.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="salesDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadSalesRecords">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table :data="salesRecords" v-loading="loading" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="卡密" width="150">
          <template #default="{ row }">
            <span class="key-string">{{ row.license_key?.key_string || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="卡密类型" width="120">
          <template #default="{ row }">
            {{ row.license_type?.name || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="销售价格" width="120">
          <template #default="{ row }">
            ¥{{ row.sale_price.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="佣金" width="120">
          <template #default="{ row }">
            ¥{{ row.commission_amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="客户信息" width="150">
          <template #default="{ row }">
            {{ row.customer_info || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="sale_date" label="销售时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.sale_date) }}
          </template>
        </el-table-column>
        <el-table-column label="备注" width="150">
          <template #default="{ row }">
            {{ row.notes || '-' }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadSalesRecords"
          @current-change="loadSalesRecords"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { ShoppingCart, Money, Coin, TrendCharts, Refresh } from '@element-plus/icons-vue'
import { ResellerService, ResellerLicenseService } from '@/api/resellerApi'
import { formatDate } from '@/utils/date'
import * as echarts from 'echarts'

// 响应式数据
const loading = ref(false)
const salesStats = ref<any>({})
const salesRecords = ref<any[]>([])
const licenseTypes = ref<any[]>([])
const salesTrendChart = ref<HTMLElement>()
const licenseTypeChart = ref<HTMLElement>()

// 日期范围
const dateRange = ref<[Date, Date] | null>(null)
const salesDateRange = ref<[Date, Date] | null>(null)

// 搜索表单
const searchForm = reactive({
  license_type_id: undefined as number | undefined
})

// 分页
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 图表实例
let salesTrendChartInstance: echarts.ECharts | null = null
let licenseTypeChartInstance: echarts.ECharts | null = null

// 方法
const loadSalesStats = async () => {
  try {
    const params = {
      start_date: dateRange.value?.[0]?.toISOString(),
      end_date: dateRange.value?.[1]?.toISOString(),
      group_by: 'day'
    }
    
    const stats = await ResellerService.getSalesStats(params)
    salesStats.value = stats
    
    // 更新图表
    updateSalesTrendChart(stats.daily_data || [])
    updateLicenseTypeChart(stats.license_type_distribution || {})
  } catch (error: any) {
    ElMessage.error(error.message || '加载销售统计失败')
  }
}

const loadSalesRecords = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.limit,
      limit: pagination.limit,
      license_type_id: searchForm.license_type_id,
      start_date: salesDateRange.value?.[0]?.toISOString(),
      end_date: salesDateRange.value?.[1]?.toISOString()
    }
    
    const response = await ResellerLicenseService.getLicenseKeySales(params)
    salesRecords.value = response.items
    pagination.total = response.total
  } catch (error: any) {
    ElMessage.error(error.message || '加载销售记录失败')
  } finally {
    loading.value = false
  }
}

const loadLicenseTypes = async () => {
  try {
    const types = await ResellerLicenseService.getAvailableLicenseTypes()
    licenseTypes.value = types
  } catch (error: any) {
    ElMessage.error(error.message || '加载卡密类型失败')
  }
}

const resetSearch = () => {
  searchForm.license_type_id = undefined
  salesDateRange.value = null
  pagination.page = 1
  loadSalesRecords()
}

const exportSalesData = () => {
  ElMessage.info('导出功能开发中')
  // TODO: 实现导出功能
}

// 图表相关方法
const initCharts = async () => {
  await nextTick()
  
  if (salesTrendChart.value) {
    salesTrendChartInstance = echarts.init(salesTrendChart.value)
  }
  
  if (licenseTypeChart.value) {
    licenseTypeChartInstance = echarts.init(licenseTypeChart.value)
  }
}

const updateSalesTrendChart = (data: any[]) => {
  if (!salesTrendChartInstance) return
  
  const option = {
    title: {
      text: '销售趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.date)
    },
    yAxis: [
      {
        type: 'value',
        name: '销售量',
        position: 'left'
      },
      {
        type: 'value',
        name: '销售额',
        position: 'right'
      }
    ],
    series: [
      {
        name: '销售量',
        type: 'bar',
        data: data.map(item => item.count),
        yAxisIndex: 0
      },
      {
        name: '销售额',
        type: 'line',
        data: data.map(item => item.amount),
        yAxisIndex: 1
      }
    ]
  }
  
  salesTrendChartInstance.setOption(option)
}

const updateLicenseTypeChart = (data: Record<string, number>) => {
  if (!licenseTypeChartInstance) return
  
  const chartData = Object.entries(data).map(([name, value]) => ({
    name,
    value
  }))
  
  const option = {
    title: {
      text: '卡密类型分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '销售量',
        type: 'pie',
        radius: '50%',
        data: chartData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  licenseTypeChartInstance.setOption(option)
}

// 窗口大小变化时重新调整图表
const handleResize = () => {
  salesTrendChartInstance?.resize()
  licenseTypeChartInstance?.resize()
}

// 初始化
onMounted(async () => {
  await initCharts()
  loadLicenseTypes()
  loadSalesStats()
  loadSalesRecords()
  
  window.addEventListener('resize', handleResize)
})

// 清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  salesTrendChartInstance?.dispose()
  licenseTypeChartInstance?.dispose()
})
</script>

<style lang="scss" scoped>
.reseller-sales {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
      align-items: center;
    }
  }
  
  .stats-cards {
    margin-bottom: 20px;
    
    .stat-card {
      position: relative;
      overflow: hidden;
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: var(--el-text-color-primary);
          margin-bottom: 8px;
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-secondary);
        }
      }
      
      .stat-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 40px;
        opacity: 0.3;
        
        &.sales { color: #409eff; }
        &.revenue { color: #67c23a; }
        &.commission { color: #e6a23c; }
        &.avg { color: #f56c6c; }
      }
    }
  }
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  .key-string {
    font-family: monospace;
    font-size: 12px;
  }
}
</style>
