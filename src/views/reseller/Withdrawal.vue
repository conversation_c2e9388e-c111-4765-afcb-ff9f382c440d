<template>
  <div class="reseller-withdrawal">
    <div class="page-header">
      <h2>提现管理</h2>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        申请提现
      </el-button>
    </div>

    <!-- 余额信息 -->
    <el-card class="balance-card" style="margin-bottom: 20px">
      <template #header>
        <span>余额信息</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="balance-item">
            <div class="balance-label">可提现余额</div>
            <div class="balance-value available">¥{{ (balance.balance || 0).toFixed(2) }}</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="balance-item">
            <div class="balance-label">冻结余额</div>
            <div class="balance-value frozen">¥{{ (balance.frozen_balance || 0).toFixed(2) }}</div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="待处理" value="pending" />
            <el-option label="已批准" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadWithdrawals">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 提现记录列表 -->
    <el-card>
      <el-table :data="withdrawals" v-loading="loading" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="提现金额" width="120">
          <template #default="{ row }">
            ¥{{ typeof row.amount === 'number' ? row.amount.toFixed(2) : row.amount }}
          </template>
        </el-table-column>
        <el-table-column prop="payment_method" label="提现方式" width="100">
          <template #default="{ row }">
            <el-tag size="small">{{ getPaymentMethodText(row.payment_method) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="payment_account" label="收款账户" width="200" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="申请时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="处理时间" width="180">
          <template #default="{ row }">
            {{ row.processed_at ? formatDate(row.processed_at) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="备注" width="150">
          <template #default="{ row }">
            {{ row.remarks || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewWithdrawal(row)">详情</el-button>
            <el-button 
              v-if="row.status === 'pending'"
              size="small" 
              type="danger" 
              @click="cancelWithdrawal(row)"
            >
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadWithdrawals"
          @current-change="loadWithdrawals"
        />
      </div>
    </el-card>

    <!-- 申请提现对话框 -->
    <el-dialog v-model="showCreateDialog" title="申请提现" width="500px">
      <el-form
        ref="withdrawalFormRef"
        :model="withdrawalForm"
        :rules="withdrawalFormRules"
        label-width="100px"
      >
        <el-form-item label="可提现余额">
          <span class="balance-display">¥{{ (balance.balance || 0).toFixed(2) }}</span>
        </el-form-item>
        <el-form-item label="提现金额" prop="amount">
          <el-input-number
            v-model="withdrawalForm.amount"
            :min="1"
            :max="balance.balance || 0"
            :precision="2"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="提现方式" prop="payment_method">
          <el-select v-model="withdrawalForm.payment_method" style="width: 100%">
            <el-option label="支付宝" value="alipay" />
            <el-option label="微信" value="wechat" />
            <el-option label="银行卡" value="bank" />
          </el-select>
        </el-form-item>
        <el-form-item label="收款账户" prop="payment_account">
          <el-input
            v-model="withdrawalForm.payment_account"
            placeholder="请输入收款账户信息"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="withdrawalForm.remarks"
            type="textarea"
            placeholder="可选备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="submitWithdrawal" :loading="submitting">
          提交申请
        </el-button>
      </template>
    </el-dialog>

    <!-- 提现详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="提现详情" width="600px">
      <div v-if="selectedWithdrawal" class="withdrawal-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ selectedWithdrawal.id }}</el-descriptions-item>
          <el-descriptions-item label="提现金额">
            ¥{{ typeof selectedWithdrawal.amount === 'number' ? selectedWithdrawal.amount.toFixed(2) : selectedWithdrawal.amount }}
          </el-descriptions-item>
          <el-descriptions-item label="提现方式">{{ getPaymentMethodText(selectedWithdrawal.payment_method) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedWithdrawal.status)">
              {{ getStatusText(selectedWithdrawal.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="收款账户" :span="2">{{ selectedWithdrawal.payment_account }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDate(selectedWithdrawal.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="处理时间">
            {{ selectedWithdrawal.processed_at ? formatDate(selectedWithdrawal.processed_at) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="申请备注" :span="2">{{ selectedWithdrawal.remarks || '-' }}</el-descriptions-item>
          <el-descriptions-item label="管理员备注" :span="2">{{ selectedWithdrawal.admin_notes || '-' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { ResellerService } from '@/api/resellerApi'
import { formatDate } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const withdrawals = ref<any[]>([])
const balance = ref({ balance: 0, frozen_balance: 0 })
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const selectedWithdrawal = ref<any>(null)

// 搜索表单
const searchForm = reactive({
  status: ''
})

const dateRange = ref<[Date, Date] | null>(null)

// 分页
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 提现表单
const withdrawalForm = reactive({
  amount: 0,
  payment_method: '',
  payment_account: '',
  remarks: ''
})

const withdrawalFormRef = ref<FormInstance>()

// 表单验证规则
const withdrawalFormRules: FormRules = {
  amount: [
    { required: true, message: '请输入提现金额', trigger: 'blur' },
    { type: 'number', min: 1, message: '提现金额必须大于0', trigger: 'blur' }
  ],
  payment_method: [
    { required: true, message: '请选择提现方式', trigger: 'change' }
  ],
  payment_account: [
    { required: true, message: '请输入收款账户', trigger: 'blur' }
  ]
}

// 方法
const loadWithdrawals = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.limit,
      limit: pagination.limit,
      status: searchForm.status || undefined,
      start_date: dateRange.value?.[0]?.toISOString(),
      end_date: dateRange.value?.[1]?.toISOString()
    }
    
    const response = await ResellerService.getWithdrawalRecords(params)
    withdrawals.value = response.items
    pagination.total = response.total
  } catch (error: any) {
    ElMessage.error(error.message || '加载提现记录失败')
  } finally {
    loading.value = false
  }
}

const loadBalance = async () => {
  try {
    const balanceData = await ResellerService.getBalance()
    balance.value = balanceData
  } catch (error: any) {
    ElMessage.error(error.message || '加载余额信息失败')
  }
}

const resetSearch = () => {
  searchForm.status = ''
  dateRange.value = null
  pagination.page = 1
  loadWithdrawals()
}

const submitWithdrawal = async () => {
  if (!withdrawalFormRef.value) return
  
  try {
    await withdrawalFormRef.value.validate()
    submitting.value = true
    
    await ResellerService.createWithdrawalRecord({
      amount: withdrawalForm.amount,
      payment_method: withdrawalForm.payment_method,
      payment_account: withdrawalForm.payment_account,
      remarks: withdrawalForm.remarks || undefined
    })
    
    ElMessage.success('提现申请提交成功')
    showCreateDialog.value = false
    resetWithdrawalForm()
    loadWithdrawals()
    loadBalance() // 重新加载余额
  } catch (error: any) {
    ElMessage.error(error.message || '提交失败')
  } finally {
    submitting.value = false
  }
}

const viewWithdrawal = (withdrawal: any) => {
  selectedWithdrawal.value = withdrawal
  showDetailDialog.value = true
}

const cancelWithdrawal = async (withdrawal: any) => {
  try {
    await ElMessageBox.confirm(
      '确定要取消这笔提现申请吗？',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await ResellerService.cancelWithdrawalRecord(withdrawal.id)
    ElMessage.success('取消成功')
    loadWithdrawals()
    loadBalance() // 重新加载余额
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '取消失败')
    }
  }
}

const resetWithdrawalForm = () => {
  Object.assign(withdrawalForm, {
    amount: 0,
    payment_method: '',
    payment_account: '',
    remarks: ''
  })
  withdrawalFormRef.value?.clearValidate()
}

// 辅助方法
const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    approved: 'primary',
    rejected: 'danger',
    completed: 'success',
    failed: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    approved: '已批准',
    rejected: '已拒绝',
    completed: '已完成',
    failed: '失败'
  }
  return texts[status] || status
}

const getPaymentMethodText = (method: string) => {
  const texts: Record<string, string> = {
    alipay: '支付宝',
    wechat: '微信',
    bank: '银行卡'
  }
  return texts[method] || method
}

// 监听对话框关闭
watch(() => showCreateDialog.value, (newVal) => {
  if (!newVal) {
    resetWithdrawalForm()
  }
})

// 初始化
onMounted(() => {
  loadBalance()
  loadWithdrawals()
})
</script>

<style lang="scss" scoped>
.reseller-withdrawal {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }
  
  .balance-card {
    .balance-item {
      text-align: center;
      
      .balance-label {
        font-size: 14px;
        color: var(--el-text-color-secondary);
        margin-bottom: 8px;
      }
      
      .balance-value {
        font-size: 24px;
        font-weight: bold;
        
        &.available {
          color: var(--el-color-success);
        }
        
        &.frozen {
          color: var(--el-color-warning);
        }
      }
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  .balance-display {
    font-size: 18px;
    font-weight: bold;
    color: var(--el-color-success);
  }
  
  .withdrawal-detail {
    .el-descriptions {
      margin-top: 20px;
    }
  }
}
</style>
