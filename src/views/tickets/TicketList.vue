<template>
  <div class="ticket-list">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>工单列表</span>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
            <el-option label="开放" value="open" />
            <el-option label="处理中" value="in_progress" />
            <el-option label="等待回复" value="awaiting_reply" />
            <el-option label="已解决" value="resolved" />
            <el-option label="已关闭" value="closed" />
            <el-option label="重新开放" value="reopened" />
          </el-select>
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="searchForm.priority" placeholder="请选择优先级" clearable>
            <el-option label="低" value="low" />
            <el-option label="中" value="medium" />
            <el-option label="高" value="high" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>
        <el-form-item label="分类">
          <el-input
            v-model="searchForm.category"
            placeholder="请输入分类"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="用户ID">
          <el-input
            v-model="searchForm.user_id"
            placeholder="请输入用户ID"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 数据表格 -->
      <el-table
        :data="tableData"
        v-loading="loading"
        stripe
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="ticket_number" label="工单号" width="120" />
        <el-table-column prop="title" label="标题" min-width="200" />
        <el-table-column prop="category" label="分类" width="120">
          <template #default="{ row }">
            {{ row.category || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="priority" label="优先级" width="100">
          <template #default="{ row }">
            <el-tag :type="getPriorityTagType(row.priority)">
              {{ getPriorityText(row.priority) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="user" label="提交用户" width="120">
          <template #default="{ row }">
            {{ row.user?.username || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="assignee" label="负责人" width="120">
          <template #default="{ row }">
            {{ row.assignee?.username || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="last_reply_at" label="最后回复" width="180">
          <template #default="{ row }">
            {{ row.last_reply_at ? formatDateTime(row.last_reply_at) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
            <el-button type="warning" size="small" @click="handleEdit(row)">编辑</el-button>
            <el-dropdown @command="(command) => handleAction(command, row)">
              <el-button type="info" size="small">
                更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="assign">分配</el-dropdown-item>
                  <el-dropdown-item command="resolve">解决</el-dropdown-item>
                  <el-dropdown-item command="close">关闭</el-dropdown-item>
                  <el-dropdown-item command="reopen" v-if="row.status === 'closed'">重新开放</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="fetchData"
          @current-change="fetchData"
        />
      </div>
    </el-card>

    <!-- 分配工单对话框 -->
    <el-dialog v-model="assignDialogVisible" title="分配工单" width="400px">
      <el-form :model="assignForm" label-width="80px">
        <el-form-item label="负责人">
          <el-select v-model="assignForm.assignee_user_id" placeholder="请选择负责人" style="width: 100%">
            <!-- TODO: 从API获取管理员列表 -->
            <el-option label="管理员1" :value="1" />
            <el-option label="管理员2" :value="2" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAssignConfirm" :loading="assignLoading">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { ArrowDown } from '@element-plus/icons-vue'
  import { useRouter } from 'vue-router'
  import { TicketService } from '@/api/adminApi'
  import type { TicketResponse } from '@/api/model/categoryModel'

  const router = useRouter()

  // 响应式数据
  const loading = ref(false)
  const tableData = ref<TicketResponse[]>([])
  const selectedRows = ref<TicketResponse[]>([])
  const assignDialogVisible = ref(false)
  const assignLoading = ref(false)
  const currentTicket = ref<TicketResponse | null>(null)

  // 搜索表单
  const searchForm = reactive({
    status: '',
    priority: '',
    category: '',
    user_id: ''
  })

  // 分配表单
  const assignForm = reactive({
    assignee_user_id: null as number | null
  })

  // 分页数据
  const pagination = reactive({
    page: 1,
    limit: 20,
    total: 0
  })

  // 获取数据
  const fetchData = async () => {
    loading.value = true
    try {
      const params = {
        skip: (pagination.page - 1) * pagination.limit,
        limit: pagination.limit,
        ...searchForm
      }

      const response = await TicketService.getTickets(params)
      // 根据API响应格式调整数据访问
      tableData.value = response.items || response.data?.items || []
      pagination.total = response.total || response.data?.total || 0
    } catch {
      ElMessage.error('获取数据失败')
    } finally {
      loading.value = false
    }
  }

  // 事件处理
  const handleSearch = () => {
    pagination.page = 1
    fetchData()
  }

  const handleReset = () => {
    Object.assign(searchForm, {
      status: '',
      priority: '',
      category: '',
      user_id: ''
    })
    handleSearch()
  }

  const handleSelectionChange = (selection: TicketResponse[]) => {
    selectedRows.value = selection
  }

  const handleView = (row: TicketResponse) => {
    router.push(`/tickets/detail/${row.id}`)
  }

  const handleEdit = (row: TicketResponse) => {
    // 跳转到编辑页面或打开编辑对话框
    console.log('编辑工单', row)
  }

  const handleAction = async (command: string, row: TicketResponse) => {
    currentTicket.value = row
    switch (command) {
      case 'assign':
        assignForm.assignee_user_id = row.assignee_user_id
        assignDialogVisible.value = true
        break
      case 'resolve':
        await handleUpdateStatus(row, 'resolved')
        break
      case 'close':
        await handleUpdateStatus(row, 'closed')
        break
      case 'reopen':
        await handleUpdateStatus(row, 'reopened')
        break
    }
  }

  const handleUpdateStatus = async (row: TicketResponse, status: string) => {
    try {
      await TicketService.updateTicketByAdmin(row.id, { status })
      ElMessage.success('状态更新成功')
      fetchData()
    } catch {
      ElMessage.error('状态更新失败')
    }
  }

  const handleAssignConfirm = async () => {
    if (!currentTicket.value || !assignForm.assignee_user_id) return

    assignLoading.value = true
    try {
      await TicketService.updateTicketByAdmin(currentTicket.value.id, {
        assignee_user_id: assignForm.assignee_user_id
      })
      ElMessage.success('分配成功')
      assignDialogVisible.value = false
      fetchData()
    } catch {
      ElMessage.error('分配失败')
    } finally {
      assignLoading.value = false
    }
  }

  // 工具函数
  const getPriorityTagType = (priority: string) => {
    const priorityMap: Record<string, string> = {
      low: 'info',
      medium: 'warning',
      high: 'danger',
      urgent: 'danger'
    }
    return priorityMap[priority] || 'info'
  }

  const getPriorityText = (priority: string) => {
    const priorityMap: Record<string, string> = {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急'
    }
    return priorityMap[priority] || priority
  }

  const getStatusTagType = (status: string) => {
    const statusMap: Record<string, string> = {
      open: 'success',
      in_progress: 'warning',
      awaiting_reply: 'info',
      resolved: 'success',
      closed: 'info',
      reopened: 'warning'
    }
    return statusMap[status] || 'info'
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      open: '开放',
      in_progress: '处理中',
      awaiting_reply: '等待回复',
      resolved: '已解决',
      closed: '已关闭',
      reopened: '重新开放'
    }
    return statusMap[status] || status
  }

  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 生命周期
  onMounted(() => {
    fetchData()
  })
</script>

<style lang="scss" scoped>
  .ticket-list {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .search-form {
      margin-bottom: 20px;
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
</style>
