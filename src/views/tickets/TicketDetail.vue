<template>
  <div class="ticket-detail" v-loading="loading">
    <el-card v-if="ticketData">
      <template #header>
        <div class="card-header">
          <span>工单详情 - {{ ticketData.ticket_number }}</span>
          <div class="header-actions">
            <el-button @click="goBack">返回</el-button>
            <el-dropdown @command="handleAction">
              <el-button type="primary">
                操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="assign">分配</el-dropdown-item>
                  <el-dropdown-item command="resolve">解决</el-dropdown-item>
                  <el-dropdown-item command="close">关闭</el-dropdown-item>
                  <el-dropdown-item command="reopen" v-if="ticketData.status === 'closed'">重新开放</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </template>

      <!-- 工单基本信息 -->
      <div class="ticket-info">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="工单号">{{ ticketData.ticket_number }}</el-descriptions-item>
          <el-descriptions-item label="标题">{{ ticketData.title }}</el-descriptions-item>
          <el-descriptions-item label="分类">{{ ticketData.category || '-' }}</el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityTagType(ticketData.priority)">
              {{ getPriorityText(ticketData.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(ticketData.status)">
              {{ getStatusText(ticketData.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="提交用户">{{ ticketData.user?.username || '-' }}</el-descriptions-item>
          <el-descriptions-item label="负责人">{{ ticketData.assignee?.username || '-' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(ticketData.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="最后更新">{{ formatDateTime(ticketData.updated_at) }}</el-descriptions-item>
          <el-descriptions-item label="解决时间">
            {{ ticketData.resolved_at ? formatDateTime(ticketData.resolved_at) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="关闭时间">
            {{ ticketData.closed_at ? formatDateTime(ticketData.closed_at) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="最后回复">
            {{ ticketData.last_reply_at ? formatDateTime(ticketData.last_reply_at) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="3">
            <div class="description-content">{{ ticketData.description }}</div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 消息列表 -->
      <div class="messages-section">
        <h3>消息记录</h3>
        <div class="messages-list">
          <div
            v-for="message in ticketData.messages"
            :key="message.id"
            class="message-item"
            :class="{ 'internal-message': message.is_internal }"
          >
            <div class="message-header">
              <div class="sender-info">
                <el-avatar :size="32" :src="message.sender?.avatar_url">
                  {{ message.sender?.username?.charAt(0) }}
                </el-avatar>
                <div class="sender-details">
                  <span class="sender-name">{{ message.sender?.username || '未知用户' }}</span>
                  <span class="message-time">{{ formatDateTime(message.created_at) }}</span>
                </div>
              </div>
              <div class="message-tags">
                <el-tag v-if="message.is_internal" type="warning" size="small">内部消息</el-tag>
              </div>
            </div>
            <div class="message-content">
              {{ message.content }}
            </div>
          </div>
        </div>
      </div>

      <!-- 回复消息 -->
      <div class="reply-section">
        <h3>添加回复</h3>
        <el-form :model="replyForm" label-width="100px">
          <el-form-item label="消息内容">
            <el-input
              v-model="replyForm.content"
              type="textarea"
              :rows="4"
              placeholder="请输入回复内容"
            />
          </el-form-item>
          <el-form-item label="内部消息">
            <el-switch v-model="replyForm.is_internal" />
            <span class="form-tip">内部消息仅管理员和客服可见</span>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleReply" :loading="replyLoading">发送回复</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 分配工单对话框 -->
    <el-dialog v-model="assignDialogVisible" title="分配工单" width="400px">
      <el-form :model="assignForm" label-width="80px">
        <el-form-item label="负责人">
          <el-select v-model="assignForm.assignee_user_id" placeholder="请选择负责人" style="width: 100%">
            <!-- TODO: 从API获取管理员列表 -->
            <el-option label="管理员1" :value="1" />
            <el-option label="管理员2" :value="2" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="assignDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleAssignConfirm" :loading="assignLoading">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { ArrowDown } from '@element-plus/icons-vue'
  import { useRouter, useRoute } from 'vue-router'
  import { TicketService } from '@/api/adminApi'
  import type { TicketDetailResponse } from '@/api/model/categoryModel'

  const router = useRouter()
  const route = useRoute()

  // 响应式数据
  const loading = ref(false)
  const ticketData = ref<TicketDetailResponse | null>(null)
  const assignDialogVisible = ref(false)
  const assignLoading = ref(false)
  const replyLoading = ref(false)

  // 回复表单
  const replyForm = reactive({
    content: '',
    is_internal: false
  })

  // 分配表单
  const assignForm = reactive({
    assignee_user_id: null as number | null
  })

  // 获取工单详情
  const fetchTicketDetail = async () => {
    const ticketId = Number(route.params.id)
    if (!ticketId) return

    loading.value = true
    try {
      const response = await TicketService.getTicketDetail(ticketId)
      ticketData.value = response
    } catch {
      ElMessage.error('获取工单详情失败')
    } finally {
      loading.value = false
    }
  }

  // 事件处理
  const goBack = () => {
    router.back()
  }

  const handleAction = async (command: string) => {
    if (!ticketData.value) return

    switch (command) {
      case 'assign':
        assignForm.assignee_user_id = ticketData.value.assignee_user_id
        assignDialogVisible.value = true
        break
      case 'resolve':
        await handleUpdateStatus('resolved')
        break
      case 'close':
        await handleUpdateStatus('closed')
        break
      case 'reopen':
        await handleUpdateStatus('reopened')
        break
    }
  }

  const handleUpdateStatus = async (status: string) => {
    if (!ticketData.value) return

    try {
      await TicketService.updateTicketByAdmin(ticketData.value.id, { status })
      ElMessage.success('状态更新成功')
      fetchTicketDetail()
    } catch {
      ElMessage.error('状态更新失败')
    }
  }

  const handleAssignConfirm = async () => {
    if (!ticketData.value || !assignForm.assignee_user_id) return

    assignLoading.value = true
    try {
      await TicketService.updateTicketByAdmin(ticketData.value.id, {
        assignee_user_id: assignForm.assignee_user_id
      })
      ElMessage.success('分配成功')
      assignDialogVisible.value = false
      fetchTicketDetail()
    } catch {
      ElMessage.error('分配失败')
    } finally {
      assignLoading.value = false
    }
  }

  const handleReply = async () => {
    if (!ticketData.value || !replyForm.content.trim()) {
      ElMessage.warning('请输入回复内容')
      return
    }

    replyLoading.value = true
    try {
      await TicketService.addTicketMessage(ticketData.value.id, {
        content: replyForm.content,
        is_internal: replyForm.is_internal
      })
      ElMessage.success('回复成功')
      replyForm.content = ''
      replyForm.is_internal = false
      fetchTicketDetail()
    } catch {
      ElMessage.error('回复失败')
    } finally {
      replyLoading.value = false
    }
  }

  // 工具函数
  const getPriorityTagType = (priority: string) => {
    const priorityMap: Record<string, string> = {
      low: 'info',
      medium: 'warning',
      high: 'danger',
      urgent: 'danger'
    }
    return priorityMap[priority] || 'info'
  }

  const getPriorityText = (priority: string) => {
    const priorityMap: Record<string, string> = {
      low: '低',
      medium: '中',
      high: '高',
      urgent: '紧急'
    }
    return priorityMap[priority] || priority
  }

  const getStatusTagType = (status: string) => {
    const statusMap: Record<string, string> = {
      open: 'success',
      in_progress: 'warning',
      awaiting_reply: 'info',
      resolved: 'success',
      closed: 'info',
      reopened: 'warning'
    }
    return statusMap[status] || 'info'
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      open: '开放',
      in_progress: '处理中',
      awaiting_reply: '等待回复',
      resolved: '已解决',
      closed: '已关闭',
      reopened: '重新开放'
    }
    return statusMap[status] || status
  }

  const formatDateTime = (dateTime: string) => {
    return new Date(dateTime).toLocaleString('zh-CN')
  }

  // 生命周期
  onMounted(() => {
    fetchTicketDetail()
  })
</script>

<style lang="scss" scoped>
  .ticket-detail {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        gap: 10px;
      }
    }

    .ticket-info {
      margin-bottom: 30px;

      .description-content {
        white-space: pre-wrap;
        line-height: 1.6;
      }
    }

    .messages-section {
      margin-bottom: 30px;

      h3 {
        margin-bottom: 20px;
        color: var(--el-text-color-primary);
      }

      .messages-list {
        .message-item {
          border: 1px solid var(--el-border-color-light);
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 16px;

          &.internal-message {
            background-color: var(--el-color-warning-light-9);
            border-color: var(--el-color-warning-light-7);
          }

          .message-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .sender-info {
              display: flex;
              align-items: center;
              gap: 12px;

              .sender-details {
                display: flex;
                flex-direction: column;

                .sender-name {
                  font-weight: 500;
                  color: var(--el-text-color-primary);
                }

                .message-time {
                  font-size: 12px;
                  color: var(--el-text-color-secondary);
                }
              }
            }
          }

          .message-content {
            white-space: pre-wrap;
            line-height: 1.6;
            color: var(--el-text-color-regular);
          }
        }
      }
    }

    .reply-section {
      h3 {
        margin-bottom: 20px;
        color: var(--el-text-color-primary);
      }

      .form-tip {
        margin-left: 10px;
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
</style>
