<template>
  <div class="reseller-management">
    <div class="page-header">
      <h2>代理商管理</h2>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        添加代理商
      </el-button>
    </div>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="搜索">
          <el-input
            v-model="searchForm.search"
            placeholder="用户名、邮箱"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="活跃" value="active" />
            <el-option label="暂停" value="suspended" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item label="级别">
          <el-select v-model="searchForm.level_id" placeholder="选择级别" clearable>
            <el-option
              v-for="level in resellerLevels"
              :key="level.id"
              :label="level.name"
              :value="level.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadResellers">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 代理商列表 -->
    <el-card>
      <el-table :data="resellers" v-loading="loading" stripe>
        <el-table-column prop="user_id" label="用户ID" width="80" />
        <el-table-column label="用户信息" width="200">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :src="row.user?.avatar_url" :size="40">
                {{ row.user?.username?.charAt(0).toUpperCase() }}
              </el-avatar>
              <div class="user-details">
                <div class="username">{{ row.user?.username }}</div>
                <div class="email">{{ row.user?.email }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="佣金率" width="100">
          <template #default="{ row }">
            {{ (row.commission_rate * 100).toFixed(1) }}%
          </template>
        </el-table-column>
        <el-table-column label="级别" width="120">
          <template #default="{ row }">
            <el-tag v-if="row.level" type="info">
              {{ row.level.name }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="总销售额" width="120">
          <template #default="{ row }">
            ¥{{ (row.total_sales || 0).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column label="总佣金" width="120">
          <template #default="{ row }">
            ¥{{ (row.total_commission || 0).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="加入时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="editReseller(row)">编辑</el-button>
            <el-button size="small" type="info" @click="viewReseller(row)">详情</el-button>
            <el-dropdown @command="(command) => handleStatusAction(row, command)">
              <el-button size="small" type="warning">
                状态<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="active" :disabled="row.status === 'active'">
                    激活
                  </el-dropdown-item>
                  <el-dropdown-item command="suspended" :disabled="row.status === 'suspended'">
                    暂停
                  </el-dropdown-item>
                  <el-dropdown-item command="disabled" :disabled="row.status === 'disabled'">
                    禁用
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadResellers"
          @current-change="loadResellers"
        />
      </div>
    </el-card>

    <!-- 创建/编辑代理商对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingReseller ? '编辑代理商' : '添加代理商'"
      width="600px"
    >
      <el-form
        ref="resellerFormRef"
        :model="resellerForm"
        :rules="resellerFormRules"
        label-width="100px"
      >
        <el-form-item label="用户ID" prop="user_id" v-if="!editingReseller">
          <el-input v-model.number="resellerForm.user_id" type="number" />
          <div class="form-tip">请输入要设置为代理商的用户ID</div>
        </el-form-item>
        <el-form-item label="佣金率" prop="commission_rate">
          <el-input-number
            v-model="resellerForm.commission_rate"
            :min="0"
            :max="1"
            :step="0.01"
            :precision="3"
            style="width: 100%"
          />
          <div class="form-tip">佣金率范围：0-1（例如：0.1 表示 10%）</div>
        </el-form-item>
        <el-form-item label="代理级别" prop="level_id">
          <el-select v-model="resellerForm.level_id" placeholder="选择代理级别" style="width: 100%">
            <el-option
              v-for="level in resellerLevels"
              :key="level.id"
              :label="`${level.name} (${(level.commission_rate * 100).toFixed(1)}%)`"
              :value="level.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveReseller" :loading="saving">
          {{ editingReseller ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 代理商详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="代理商详情" width="800px">
      <div v-if="selectedReseller" class="reseller-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ selectedReseller.user_id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ selectedReseller.user?.username }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ selectedReseller.user?.email || '-' }}</el-descriptions-item>
          <el-descriptions-item label="佣金率">{{ (selectedReseller.commission_rate * 100).toFixed(1) }}%</el-descriptions-item>
          <el-descriptions-item label="代理级别">
            {{ selectedReseller.level?.name || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedReseller.status)">
              {{ getStatusText(selectedReseller.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="总销售额">¥{{ (selectedReseller.total_sales || 0).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="总佣金">¥{{ (selectedReseller.total_commission || 0).toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="加入时间" :span="2">
            {{ formatDate(selectedReseller.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后更新" :span="2">
            {{ formatDate(selectedReseller.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, ArrowDown } from '@element-plus/icons-vue'
import { AdminResellerService } from '@/api/adminApi'
import type { ResellerDetailResponse, ResellerLevelResponse } from '@/api/model/adminModel'
import { formatDate } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const resellers = ref<ResellerDetailResponse[]>([])
const resellerLevels = ref<ResellerLevelResponse[]>([])
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const editingReseller = ref<ResellerDetailResponse | null>(null)
const selectedReseller = ref<ResellerDetailResponse | null>(null)

// 搜索表单
const searchForm = reactive({
  search: '',
  status: '',
  level_id: undefined as number | undefined
})

// 分页
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 代理商表单
const resellerForm = reactive({
  user_id: undefined as number | undefined,
  commission_rate: 0.1,
  level_id: undefined as number | undefined
})

const resellerFormRef = ref<FormInstance>()

// 表单验证规则
const resellerFormRules: FormRules = {
  user_id: [
    { required: true, message: '请输入用户ID', trigger: 'blur' }
  ],
  commission_rate: [
    { required: true, message: '请输入佣金率', trigger: 'blur' },
    { type: 'number', min: 0, max: 1, message: '佣金率必须在 0-1 之间', trigger: 'blur' }
  ]
}

// 方法
const loadResellers = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.limit,
      limit: pagination.limit,
      search: searchForm.search || undefined,
      status: searchForm.status || undefined,
      level_id: searchForm.level_id
    }
    
    const response = await AdminResellerService.getResellers(params)
    resellers.value = response.items
    pagination.total = response.total
  } catch (error: any) {
    ElMessage.error(error.message || '加载代理商列表失败')
  } finally {
    loading.value = false
  }
}

const loadResellerLevels = async () => {
  try {
    // 这里需要添加获取代理商级别的API
    // const levels = await AdminResellerService.getResellerLevels()
    // resellerLevels.value = levels
    
    // 临时模拟数据
    resellerLevels.value = [
      { id: 1, name: '初级代理', commission_rate: 0.05, is_active: true, created_at: '' },
      { id: 2, name: '中级代理', commission_rate: 0.08, is_active: true, created_at: '' },
      { id: 3, name: '高级代理', commission_rate: 0.12, is_active: true, created_at: '' }
    ]
  } catch (error: any) {
    ElMessage.error(error.message || '加载代理商级别失败')
  }
}

const resetSearch = () => {
  searchForm.search = ''
  searchForm.status = ''
  searchForm.level_id = undefined
  pagination.page = 1
  loadResellers()
}

const editReseller = (reseller: ResellerDetailResponse) => {
  editingReseller.value = reseller
  Object.assign(resellerForm, {
    user_id: reseller.user_id,
    commission_rate: reseller.commission_rate,
    level_id: reseller.level_id
  })
  showCreateDialog.value = true
}

const viewReseller = (reseller: ResellerDetailResponse) => {
  selectedReseller.value = reseller
  showDetailDialog.value = true
}

const handleStatusAction = async (reseller: ResellerDetailResponse, status: string) => {
  try {
    await ElMessageBox.confirm(
      `确定要将代理商状态更改为"${getStatusText(status)}"吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await AdminResellerService.updateResellerStatus(reseller.user_id, {
      status,
      remarks: `管理员操作：状态更改为${getStatusText(status)}`
    })
    
    ElMessage.success('状态更新成功')
    loadResellers()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '状态更新失败')
    }
  }
}

const saveReseller = async () => {
  if (!resellerFormRef.value) return
  
  try {
    await resellerFormRef.value.validate()
    saving.value = true
    
    if (editingReseller.value) {
      // 更新代理商
      await AdminResellerService.updateReseller(editingReseller.value.user_id, {
        commission_rate: resellerForm.commission_rate,
        level_id: resellerForm.level_id
      })
      ElMessage.success('更新成功')
    } else {
      // 创建代理商
      await AdminResellerService.createReseller({
        user_id: resellerForm.user_id!,
        commission_rate: resellerForm.commission_rate,
        level_id: resellerForm.level_id
      })
      ElMessage.success('创建成功')
    }
    
    showCreateDialog.value = false
    editingReseller.value = null
    resetResellerForm()
    loadResellers()
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败')
  } finally {
    saving.value = false
  }
}

const resetResellerForm = () => {
  Object.assign(resellerForm, {
    user_id: undefined,
    commission_rate: 0.1,
    level_id: undefined
  })
  resellerFormRef.value?.clearValidate()
}

// 辅助方法
const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    active: 'success',
    suspended: 'warning',
    disabled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    active: '活跃',
    suspended: '暂停',
    disabled: '禁用'
  }
  return texts[status] || status
}

// 监听对话框关闭
watch(() => showCreateDialog.value, (newVal) => {
  if (!newVal) {
    editingReseller.value = null
    resetResellerForm()
  }
})

// 初始化
onMounted(() => {
  loadResellers()
  loadResellerLevels()
})
</script>

<style lang="scss" scoped>
.reseller-management {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  .user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    
    .user-details {
      .username {
        font-weight: 500;
        color: var(--el-text-color-primary);
      }
      
      .email {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
  
  .reseller-detail {
    .el-descriptions {
      margin-top: 20px;
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 4px;
  }
}
</style>
