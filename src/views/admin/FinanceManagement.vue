<template>
  <div class="finance-management">
    <div class="page-header">
      <h2>财务管理</h2>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ totalRevenue.toFixed(2) }}</div>
              <div class="stat-label">总收入</div>
            </div>
            <el-icon class="stat-icon revenue"><Money /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ totalCommission.toFixed(2) }}</div>
              <div class="stat-label">总佣金</div>
            </div>
            <el-icon class="stat-icon commission"><Coin /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">{{ pendingWithdrawals }}</div>
              <div class="stat-label">待处理提现</div>
            </div>
            <el-icon class="stat-icon pending"><Clock /></el-icon>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-value">¥{{ monthlyRevenue.toFixed(2) }}</div>
              <div class="stat-label">本月收入</div>
            </div>
            <el-icon class="stat-icon monthly"><TrendCharts /></el-icon>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 标签页 -->
    <el-tabs v-model="activeTab" class="finance-tabs">
      <!-- 佣金记录 -->
      <el-tab-pane label="佣金记录" name="commission">
        <div class="tab-content">
          <!-- 搜索筛选 -->
          <el-card class="search-card">
            <el-form :model="commissionSearchForm" inline>
              <el-form-item label="代理商ID">
                <el-input
                  v-model.number="commissionSearchForm.reseller_user_id"
                  placeholder="代理商用户ID"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="commissionSearchForm.status" placeholder="选择状态" clearable>
                  <el-option label="待结算" value="pending" />
                  <el-option label="已结算" value="settled" />
                  <el-option label="已取消" value="cancelled" />
                </el-select>
              </el-form-item>
              <el-form-item label="时间范围">
                <el-date-picker
                  v-model="commissionDateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="loadCommissionRecords">搜索</el-button>
                <el-button @click="resetCommissionSearch">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 佣金记录列表 -->
          <el-card>
            <el-table :data="commissionRecords" v-loading="commissionLoading" stripe>
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column label="代理商" width="120">
                <template #default="{ row }">
                  {{ row.reseller?.username || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="卡密" width="150">
                <template #default="{ row }">
                  {{ row.license_key?.key_string || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="销售金额" width="120">
                <template #default="{ row }">
                  ¥{{ row.sale_amount.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="佣金率" width="100">
                <template #default="{ row }">
                  {{ (row.commission_rate * 100).toFixed(1) }}%
                </template>
              </el-table-column>
              <el-table-column label="佣金金额" width="120">
                <template #default="{ row }">
                  ¥{{ row.commission_amount.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getCommissionStatusTagType(row.status)">
                    {{ getCommissionStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="创建时间" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.created_at) }}
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="commissionPagination.page"
                v-model:page-size="commissionPagination.limit"
                :total="commissionPagination.total"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="loadCommissionRecords"
                @current-change="loadCommissionRecords"
              />
            </div>
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 提现记录 -->
      <el-tab-pane label="提现记录" name="withdrawal">
        <div class="tab-content">
          <!-- 搜索筛选 -->
          <el-card class="search-card">
            <el-form :model="withdrawalSearchForm" inline>
              <el-form-item label="代理商ID">
                <el-input
                  v-model.number="withdrawalSearchForm.reseller_user_id"
                  placeholder="代理商用户ID"
                  clearable
                  style="width: 150px"
                />
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="withdrawalSearchForm.status" placeholder="选择状态" clearable>
                  <el-option label="待处理" value="pending" />
                  <el-option label="已批准" value="approved" />
                  <el-option label="已拒绝" value="rejected" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="失败" value="failed" />
                </el-select>
              </el-form-item>
              <el-form-item label="时间范围">
                <el-date-picker
                  v-model="withdrawalDateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 240px"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="loadWithdrawalRecords">搜索</el-button>
                <el-button @click="resetWithdrawalSearch">重置</el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <!-- 提现记录列表 -->
          <el-card>
            <el-table :data="withdrawalRecords" v-loading="withdrawalLoading" stripe>
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column label="代理商" width="120">
                <template #default="{ row }">
                  {{ row.reseller_username || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="提现金额" width="120">
                <template #default="{ row }">
                  ¥{{ typeof row.amount === 'number' ? row.amount.toFixed(2) : row.amount }}
                </template>
              </el-table-column>
              <el-table-column prop="payment_method" label="提现方式" width="120" />
              <el-table-column prop="payment_account" label="提现账户" width="150" />
              <el-table-column label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getWithdrawalStatusTagType(row.status)">
                    {{ getWithdrawalStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="申请时间" width="180">
                <template #default="{ row }">
                  {{ formatDate(row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column label="处理时间" width="180">
                <template #default="{ row }">
                  {{ row.processed_at ? formatDate(row.processed_at) : '-' }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template #default="{ row }">
                  <el-button size="small" @click="viewWithdrawal(row)">详情</el-button>
                  <el-button 
                    v-if="row.status === 'pending'"
                    size="small" 
                    type="success" 
                    @click="processWithdrawal(row, 'approved')"
                  >
                    批准
                  </el-button>
                  <el-button 
                    v-if="row.status === 'pending'"
                    size="small" 
                    type="danger" 
                    @click="processWithdrawal(row, 'rejected')"
                  >
                    拒绝
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
              <el-pagination
                v-model:current-page="withdrawalPagination.page"
                v-model:page-size="withdrawalPagination.limit"
                :total="withdrawalPagination.total"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="loadWithdrawalRecords"
                @current-change="loadWithdrawalRecords"
              />
            </div>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 提现详情对话框 -->
    <el-dialog v-model="showWithdrawalDialog" title="提现详情" width="600px">
      <div v-if="selectedWithdrawal" class="withdrawal-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ selectedWithdrawal.id }}</el-descriptions-item>
          <el-descriptions-item label="代理商">{{ selectedWithdrawal.reseller_username }}</el-descriptions-item>
          <el-descriptions-item label="提现金额">
            ¥{{ typeof selectedWithdrawal.amount === 'number' ? selectedWithdrawal.amount.toFixed(2) : selectedWithdrawal.amount }}
          </el-descriptions-item>
          <el-descriptions-item label="提现方式">{{ selectedWithdrawal.payment_method }}</el-descriptions-item>
          <el-descriptions-item label="提现账户" :span="2">{{ selectedWithdrawal.payment_account }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getWithdrawalStatusTagType(selectedWithdrawal.status)">
              {{ getWithdrawalStatusText(selectedWithdrawal.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDate(selectedWithdrawal.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="处理时间">
            {{ selectedWithdrawal.processed_at ? formatDate(selectedWithdrawal.processed_at) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="用户备注" :span="2">{{ selectedWithdrawal.remarks || '-' }}</el-descriptions-item>
          <el-descriptions-item label="管理员备注" :span="2">{{ selectedWithdrawal.admin_notes || '-' }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 处理操作 -->
        <div v-if="selectedWithdrawal.status === 'pending'" class="process-actions">
          <el-divider>处理操作</el-divider>
          <el-form :model="processForm" label-width="100px">
            <el-form-item label="处理结果">
              <el-radio-group v-model="processForm.status">
                <el-radio value="approved">批准</el-radio>
                <el-radio value="rejected">拒绝</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="管理员备注">
              <el-input
                v-model="processForm.admin_notes"
                type="textarea"
                placeholder="请输入处理备注"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="submitProcess" :loading="processing">
                提交处理
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Money, Coin, Clock, TrendCharts } from '@element-plus/icons-vue'
import { AdminFinanceService } from '@/api/adminApi'
import type { CommissionRecordResponse, WithdrawalRecordResponse } from '@/api/model/adminModel'
import { formatDate } from '@/utils/date'

// 响应式数据
const activeTab = ref('commission')
const commissionLoading = ref(false)
const withdrawalLoading = ref(false)
const processing = ref(false)
const commissionRecords = ref<CommissionRecordResponse[]>([])
const withdrawalRecords = ref<WithdrawalRecordResponse[]>([])
const showWithdrawalDialog = ref(false)
const selectedWithdrawal = ref<WithdrawalRecordResponse | null>(null)

// 统计数据
const totalRevenue = ref(0)
const totalCommission = ref(0)
const pendingWithdrawals = ref(0)
const monthlyRevenue = ref(0)

// 搜索表单
const commissionSearchForm = reactive({
  reseller_user_id: undefined as number | undefined,
  status: '',
})

const withdrawalSearchForm = reactive({
  reseller_user_id: undefined as number | undefined,
  status: ''
})

const commissionDateRange = ref<[Date, Date] | null>(null)
const withdrawalDateRange = ref<[Date, Date] | null>(null)

// 分页
const commissionPagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

const withdrawalPagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 处理表单
const processForm = reactive({
  status: 'approved' as 'approved' | 'rejected',
  admin_notes: ''
})

// 方法
const loadCommissionRecords = async () => {
  commissionLoading.value = true
  try {
    const params = {
      skip: (commissionPagination.page - 1) * commissionPagination.limit,
      limit: commissionPagination.limit,
      reseller_user_id: commissionSearchForm.reseller_user_id,
      status: commissionSearchForm.status || undefined,
      start_date: commissionDateRange.value?.[0]?.toISOString(),
      end_date: commissionDateRange.value?.[1]?.toISOString()
    }
    
    const response = await AdminFinanceService.getCommissionRecords(params)
    commissionRecords.value = response.items
    commissionPagination.total = response.total
  } catch (error: any) {
    ElMessage.error(error.message || '加载佣金记录失败')
  } finally {
    commissionLoading.value = false
  }
}

const loadWithdrawalRecords = async () => {
  withdrawalLoading.value = true
  try {
    const params = {
      skip: (withdrawalPagination.page - 1) * withdrawalPagination.limit,
      limit: withdrawalPagination.limit,
      reseller_user_id: withdrawalSearchForm.reseller_user_id,
      status: withdrawalSearchForm.status || undefined,
      start_date: withdrawalDateRange.value?.[0]?.toISOString(),
      end_date: withdrawalDateRange.value?.[1]?.toISOString()
    }
    
    const response = await AdminFinanceService.getWithdrawalRecords(params)
    withdrawalRecords.value = response.items
    withdrawalPagination.total = response.total
    
    // 更新待处理提现数量
    pendingWithdrawals.value = response.items.filter(item => item.status === 'pending').length
  } catch (error: any) {
    ElMessage.error(error.message || '加载提现记录失败')
  } finally {
    withdrawalLoading.value = false
  }
}

const resetCommissionSearch = () => {
  commissionSearchForm.reseller_user_id = undefined
  commissionSearchForm.status = ''
  commissionDateRange.value = null
  commissionPagination.page = 1
  loadCommissionRecords()
}

const resetWithdrawalSearch = () => {
  withdrawalSearchForm.reseller_user_id = undefined
  withdrawalSearchForm.status = ''
  withdrawalDateRange.value = null
  withdrawalPagination.page = 1
  loadWithdrawalRecords()
}

const viewWithdrawal = (withdrawal: WithdrawalRecordResponse) => {
  selectedWithdrawal.value = withdrawal
  processForm.status = 'approved'
  processForm.admin_notes = ''
  showWithdrawalDialog.value = true
}

const processWithdrawal = async (withdrawal: WithdrawalRecordResponse, status: 'approved' | 'rejected') => {
  try {
    const action = status === 'approved' ? '批准' : '拒绝'
    await ElMessageBox.confirm(
      `确定要${action}这笔提现申请吗？`,
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await AdminFinanceService.processWithdrawalRecord(withdrawal.id, {
      status,
      admin_notes: `管理员${action}操作`
    })
    
    ElMessage.success(`${action}成功`)
    loadWithdrawalRecords()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '操作失败')
    }
  }
}

const submitProcess = async () => {
  if (!selectedWithdrawal.value) return
  
  try {
    processing.value = true
    
    await AdminFinanceService.processWithdrawalRecord(selectedWithdrawal.value.id, {
      status: processForm.status,
      admin_notes: processForm.admin_notes
    })
    
    ElMessage.success('处理成功')
    showWithdrawalDialog.value = false
    loadWithdrawalRecords()
  } catch (error: any) {
    ElMessage.error(error.message || '处理失败')
  } finally {
    processing.value = false
  }
}

// 辅助方法
const getCommissionStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    settled: 'success',
    cancelled: 'danger'
  }
  return types[status] || 'info'
}

const getCommissionStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待结算',
    settled: '已结算',
    cancelled: '已取消'
  }
  return texts[status] || status
}

const getWithdrawalStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    approved: 'primary',
    rejected: 'danger',
    completed: 'success',
    failed: 'danger'
  }
  return types[status] || 'info'
}

const getWithdrawalStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    approved: '已批准',
    rejected: '已拒绝',
    completed: '已完成',
    failed: '失败'
  }
  return texts[status] || status
}

// 初始化
onMounted(() => {
  loadCommissionRecords()
  loadWithdrawalRecords()
})
</script>

<style lang="scss" scoped>
.finance-management {
  .page-header {
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }
  
  .stats-cards {
    margin-bottom: 20px;
    
    .stat-card {
      position: relative;
      overflow: hidden;
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: var(--el-text-color-primary);
          margin-bottom: 8px;
        }
        
        .stat-label {
          font-size: 14px;
          color: var(--el-text-color-secondary);
        }
      }
      
      .stat-icon {
        position: absolute;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 40px;
        opacity: 0.3;
        
        &.revenue { color: #67c23a; }
        &.commission { color: #e6a23c; }
        &.pending { color: #f56c6c; }
        &.monthly { color: #409eff; }
      }
    }
  }
  
  .finance-tabs {
    .tab-content {
      .search-card {
        margin-bottom: 20px;
      }
      
      .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 20px;
      }
    }
  }
  
  .withdrawal-detail {
    .process-actions {
      margin-top: 20px;
    }
  }
}
</style>
