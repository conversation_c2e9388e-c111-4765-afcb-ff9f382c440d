<template>
  <div class="license-management">
    <div class="page-header">
      <h2>卡密管理</h2>
      <div class="header-actions">
        <el-button type="success" @click="showBatchGenerateDialog = true">
          <el-icon><Plus /></el-icon>
          批量生成卡密
        </el-button>
        <el-button type="primary" @click="loadLicenseTypes">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 卡密类型管理 -->
    <el-card class="types-card" style="margin-bottom: 20px">
      <template #header>
        <div class="card-header">
          <span>卡密类型</span>
          <el-button size="small" type="primary" @click="showTypeDialog = true">
            添加类型
          </el-button>
        </div>
      </template>
      <el-table :data="licenseTypes" size="small">
        <el-table-column prop="id" label="ID" width="60" />
        <el-table-column prop="code" label="类型代码" width="120" />
        <el-table-column prop="name" label="类型名称" width="150" />
        <el-table-column label="时长" width="100">
          <template #default="{ row }">
            {{ row.duration_days ? `${row.duration_days}天` : '永久' }}
          </template>
        </el-table-column>
        <el-table-column prop="max_concurrent_devices" label="最大设备数" width="100" />
        <el-table-column label="价格" width="100">
          <template #default="{ row }">
            {{ row.price ? `¥${row.price}` : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.is_active ? 'success' : 'danger'">
              {{ row.is_active ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button size="small" @click="editLicenseType(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 搜索筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="搜索">
          <el-input
            v-model="searchForm.search"
            placeholder="卡密字符串"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="searchForm.license_type_id" placeholder="选择类型" clearable>
            <el-option
              v-for="type in licenseTypes"
              :key="type.id"
              :label="type.name"
              :value="type.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="未使用" value="unused" />
            <el-option label="已使用" value="used" />
            <el-option label="已过期" value="expired" />
            <el-option label="已禁用" value="disabled" />
          </el-select>
        </el-form-item>
        <el-form-item label="代理商">
          <el-input
            v-model.number="searchForm.reseller_user_id"
            placeholder="代理商用户ID"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="loadLicenseKeys">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 卡密列表 -->
    <el-card>
      <el-table :data="licenseKeys" v-loading="loading" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="key_string" label="卡密" width="200">
          <template #default="{ row }">
            <div class="key-string">
              <span>{{ row.key_string }}</span>
              <el-button
                size="small"
                text
                @click="copyToClipboard(row.key_string)"
                style="margin-left: 8px"
              >
                <el-icon><DocumentCopy /></el-icon>
              </el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="类型" width="120">
          <template #default="{ row }">
            {{ row.license_type?.name || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="代理商" width="120">
          <template #default="{ row }">
            {{ row.reseller?.username || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="使用用户" width="120">
          <template #default="{ row }">
            {{ row.user?.username || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="激活时间" width="180">
          <template #default="{ row }">
            {{ row.activated_at ? formatDate(row.activated_at) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="过期时间" width="180">
          <template #default="{ row }">
            {{ row.expires_at ? formatDate(row.expires_at) : '永久' }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewLicenseKey(row)">详情</el-button>
            <el-button size="small" type="warning" @click="editLicenseKey(row)">编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteLicenseKey(row)"
              :disabled="row.status === 'used'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadLicenseKeys"
          @current-change="loadLicenseKeys"
        />
      </div>
    </el-card>

    <!-- 批量生成卡密对话框 -->
    <el-dialog v-model="showBatchGenerateDialog" title="批量生成卡密" width="500px">
      <el-form
        ref="batchFormRef"
        :model="batchForm"
        :rules="batchFormRules"
        label-width="120px"
      >
        <el-form-item label="卡密类型" prop="license_type_id">
          <el-select v-model="batchForm.license_type_id" style="width: 100%">
            <el-option
              v-for="type in licenseTypes.filter(t => t.is_active)"
              :key="type.id"
              :label="type.name"
              :value="type.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="生成数量" prop="quantity">
          <el-input-number
            v-model="batchForm.quantity"
            :min="1"
            :max="1000"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="分配代理商">
          <el-input
            v-model.number="batchForm.reseller_user_id"
            placeholder="代理商用户ID（可选）"
            type="number"
          />
        </el-form-item>
        <el-form-item label="过期时间">
          <el-date-picker
            v-model="batchForm.expires_at"
            type="datetime"
            placeholder="选择过期时间（可选）"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="batchForm.notes"
            type="textarea"
            placeholder="生成备注（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showBatchGenerateDialog = false">取消</el-button>
        <el-button type="primary" @click="batchGenerate" :loading="generating">
          生成
        </el-button>
      </template>
    </el-dialog>

    <!-- 卡密详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="卡密详情" width="600px">
      <div v-if="selectedLicenseKey" class="license-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="ID">{{ selectedLicenseKey.id }}</el-descriptions-item>
          <el-descriptions-item label="卡密">{{ selectedLicenseKey.key_string }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ selectedLicenseKey.license_type?.name }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(selectedLicenseKey.status)">
              {{ getStatusText(selectedLicenseKey.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="代理商">
            {{ selectedLicenseKey.reseller?.username || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="使用用户">
            {{ selectedLicenseKey.user?.username || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="激活时间">
            {{ selectedLicenseKey.activated_at ? formatDate(selectedLicenseKey.activated_at) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="过期时间">
            {{ selectedLicenseKey.expires_at ? formatDate(selectedLicenseKey.expires_at) : '永久' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ formatDate(selectedLicenseKey.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" :span="2">
            {{ formatDate(selectedLicenseKey.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Plus, Refresh, DocumentCopy } from '@element-plus/icons-vue'
import { AdminLicenseKeyService } from '@/api/adminApi'
import type { LicenseKeyResponse, LicenseTypeResponse } from '@/api/model/adminModel'
import { formatDate } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const generating = ref(false)
const licenseKeys = ref<LicenseKeyResponse[]>([])
const licenseTypes = ref<LicenseTypeResponse[]>([])
const showBatchGenerateDialog = ref(false)
const showDetailDialog = ref(false)
const showTypeDialog = ref(false)
const selectedLicenseKey = ref<LicenseKeyResponse | null>(null)

// 搜索表单
const searchForm = reactive({
  search: '',
  license_type_id: undefined as number | undefined,
  status: '',
  reseller_user_id: undefined as number | undefined
})

// 分页
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 批量生成表单
const batchForm = reactive({
  license_type_id: undefined as number | undefined,
  quantity: 10,
  reseller_user_id: undefined as number | undefined,
  expires_at: null as Date | null,
  notes: ''
})

const batchFormRef = ref<FormInstance>()

// 表单验证规则
const batchFormRules: FormRules = {
  license_type_id: [
    { required: true, message: '请选择卡密类型', trigger: 'change' }
  ],
  quantity: [
    { required: true, message: '请输入生成数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 1000, message: '数量必须在 1-1000 之间', trigger: 'blur' }
  ]
}

// 方法
const loadLicenseKeys = async () => {
  loading.value = true
  try {
    const params = {
      skip: (pagination.page - 1) * pagination.limit,
      limit: pagination.limit,
      search: searchForm.search || undefined,
      license_type_id: searchForm.license_type_id,
      status: searchForm.status || undefined,
      reseller_user_id: searchForm.reseller_user_id
    }
    
    const response = await AdminLicenseKeyService.getLicenseKeys(params)
    licenseKeys.value = response.items
    pagination.total = response.total
  } catch (error: any) {
    ElMessage.error(error.message || '加载卡密列表失败')
  } finally {
    loading.value = false
  }
}

const loadLicenseTypes = async () => {
  try {
    const types = await AdminLicenseKeyService.getLicenseTypes()
    licenseTypes.value = types
  } catch (error: any) {
    ElMessage.error(error.message || '加载卡密类型失败')
  }
}

const resetSearch = () => {
  searchForm.search = ''
  searchForm.license_type_id = undefined
  searchForm.status = ''
  searchForm.reseller_user_id = undefined
  pagination.page = 1
  loadLicenseKeys()
}

const batchGenerate = async () => {
  if (!batchFormRef.value) return
  
  try {
    await batchFormRef.value.validate()
    generating.value = true
    
    const data = {
      license_type_id: batchForm.license_type_id!,
      quantity: batchForm.quantity,
      reseller_user_id: batchForm.reseller_user_id || undefined,
      expires_at: batchForm.expires_at ? batchForm.expires_at.toISOString() : undefined,
      notes: batchForm.notes || undefined
    }
    
    const response = await AdminLicenseKeyService.batchGenerate(data)
    ElMessage.success(`批量生成任务已提交，任务ID: ${response.task_id}`)
    
    showBatchGenerateDialog.value = false
    resetBatchForm()
    loadLicenseKeys()
  } catch (error: any) {
    ElMessage.error(error.message || '批量生成失败')
  } finally {
    generating.value = false
  }
}

const viewLicenseKey = (licenseKey: LicenseKeyResponse) => {
  selectedLicenseKey.value = licenseKey
  showDetailDialog.value = true
}

const editLicenseKey = (licenseKey: LicenseKeyResponse) => {
  // TODO: 实现编辑功能
  ElMessage.info('编辑功能开发中')
}

const deleteLicenseKey = async (licenseKey: LicenseKeyResponse) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除卡密 "${licenseKey.key_string}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await AdminLicenseKeyService.deleteLicenseKey(licenseKey.id)
    ElMessage.success('删除成功')
    loadLicenseKeys()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const resetBatchForm = () => {
  Object.assign(batchForm, {
    license_type_id: undefined,
    quantity: 10,
    reseller_user_id: undefined,
    expires_at: null,
    notes: ''
  })
  batchFormRef.value?.clearValidate()
}

const editLicenseType = (type: LicenseTypeResponse) => {
  // TODO: 实现编辑卡密类型功能
  ElMessage.info('编辑卡密类型功能开发中')
}

// 辅助方法
const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    unused: 'info',
    used: 'success',
    expired: 'warning',
    disabled: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    unused: '未使用',
    used: '已使用',
    expired: '已过期',
    disabled: '已禁用'
  }
  return texts[status] || status
}

// 监听对话框关闭
watch(() => showBatchGenerateDialog.value, (newVal) => {
  if (!newVal) {
    resetBatchForm()
  }
})

// 初始化
onMounted(() => {
  loadLicenseTypes()
  loadLicenseKeys()
})
</script>

<style lang="scss" scoped>
.license-management {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .types-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .search-card {
    margin-bottom: 20px;
  }
  
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  .key-string {
    display: flex;
    align-items: center;
    font-family: monospace;
  }
  
  .license-detail {
    .el-descriptions {
      margin-top: 20px;
    }
  }
}
</style>
