<template>
  <div class="reseller-levels">
    <div class="page-header">
      <h2>代理商级别管理</h2>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        新增级别
      </el-button>
    </div>

    <!-- 级别列表 -->
    <el-card>
      <el-table :data="levels" v-loading="loading" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="级别名称" width="150" />
        <el-table-column label="佣金率" width="120">
          <template #default="{ row }">
            {{ (row.commission_rate * 100).toFixed(2) }}%
          </template>
        </el-table-column>
        <el-table-column label="最低销售额" width="150">
          <template #default="{ row }">
            ¥{{ row.min_sales_amount?.toFixed(2) || '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column label="代理商数量" width="120">
          <template #default="{ row }">
            {{ row.reseller_count || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editLevel(row)">
              编辑
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="deleteLevel(row)"
              :disabled="row.reseller_count > 0"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="级别名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入级别名称" />
        </el-form-item>
        
        <el-form-item label="佣金率" prop="commission_rate">
          <el-input-number
            v-model="form.commission_rate"
            :min="0"
            :max="1"
            :step="0.01"
            :precision="4"
            placeholder="请输入佣金率"
            style="width: 100%"
          />
          <div class="form-tip">佣金率范围：0-1，例如0.1表示10%</div>
        </el-form-item>
        
        <el-form-item label="最低销售额" prop="min_sales_amount">
          <el-input-number
            v-model="form.min_sales_amount"
            :min="0"
            :step="100"
            :precision="2"
            placeholder="请输入最低销售额"
            style="width: 100%"
          />
          <div class="form-tip">达到此销售额才能升级到该级别</div>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入级别描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { AdminResellerLevelService } from '@/api/adminApi'
import { formatDate } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const levels = ref<any[]>([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const editingId = ref<number | null>(null)

// 表单数据
const form = reactive({
  name: '',
  commission_rate: 0,
  min_sales_amount: 0,
  description: ''
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入级别名称', trigger: 'blur' }
  ],
  commission_rate: [
    { required: true, message: '请输入佣金率', trigger: 'blur' },
    { type: 'number', min: 0, max: 1, message: '佣金率必须在0-1之间', trigger: 'blur' }
  ]
}

const formRef = ref()

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑级别' : '新增级别')

// 方法
const loadLevels = async () => {
  loading.value = true
  try {
    const response = await AdminResellerLevelService.getResellerLevels()
    levels.value = response
  } catch (error: any) {
    ElMessage.error(error.message || '加载级别列表失败')
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  isEdit.value = false
  editingId.value = null
  dialogVisible.value = true
}

const editLevel = (level: any) => {
  isEdit.value = true
  editingId.value = level.id
  form.name = level.name
  form.commission_rate = level.commission_rate
  form.min_sales_amount = level.min_sales_amount || 0
  form.description = level.description || ''
  dialogVisible.value = true
}

const deleteLevel = async (level: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除级别"${level.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await AdminResellerLevelService.deleteResellerLevel(level.id)
    ElMessage.success('删除成功')
    loadLevels()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

const submitForm = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true
    
    const data = {
      name: form.name,
      commission_rate: form.commission_rate,
      min_sales_amount: form.min_sales_amount,
      description: form.description
    }
    
    if (isEdit.value && editingId.value) {
      await AdminResellerLevelService.updateResellerLevel(editingId.value, data)
      ElMessage.success('更新成功')
    } else {
      await AdminResellerLevelService.createResellerLevel(data)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadLevels()
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  form.name = ''
  form.commission_rate = 0
  form.min_sales_amount = 0
  form.description = ''
  formRef.value?.resetFields()
}

// 初始化
onMounted(() => {
  loadLevels()
})
</script>

<style lang="scss" scoped>
.reseller-levels {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: var(--el-text-color-primary);
    }
  }
  
  .form-tip {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    margin-top: 5px;
  }
}
</style>
