import { RoutesAlias } from '../routesAlias'
import { MenuListType } from '@/types/menu'

/**
 * 菜单列表、异步路由
 *
 * 支持两种模式:
 * 1. 前端静态配置 - 直接使用本文件中定义的路由配置
 * 2. 后端动态配置 - 后端返回菜单数据，前端解析生成路由
 *
 * 菜单标题（title）:
 * 可以是 i18n 的 key，也可以是字符串，比如：'用户列表'
 */
export const asyncRoutes: MenuListType[] = [
  {
    id: 1,
    name: 'Dashboard',
    path: '/dashboard',
    component: RoutesAlias.Home,
    meta: {
      title: '数据概览',
      icon: '&#xe721;',
      keepAlive: false
    },
    children: [
      {
        id: 11,
        path: 'console',
        name: 'Console',
        component: RoutesAlias.Dashboard,
        meta: {
          title: '控制台',
          keepAlive: false,
          fixedTab: true
        }
      }
    ]
  },
  // Widgets菜单已移除，文件保留用于参考
  // Template菜单已移除，文件保留用于参考
  {
    id: 2,
    path: '/system',
    name: 'System',
    component: RoutesAlias.Home,
    meta: {
      title: '系统管理',
      icon: '&#xe7b9;',
      keepAlive: false,
      roles: ['admin']
    },
    children: [
      {
        id: 21,
        path: 'user-center',
        name: 'UserCenter',
        component: RoutesAlias.UserCenter,
        meta: {
          title: '个人中心',
          isHide: true,
          keepAlive: true,
          isHideTab: true
        }
      },
      {
        id: 22,
        path: 'menu',
        name: 'Menus',
        component: RoutesAlias.Menu,
        meta: {
          title: '菜单管理',
          keepAlive: true,
          roles: ['admin']
        }
      },
      {
        id: 23,
        path: 'health',
        name: 'SystemHealth',
        component: RoutesAlias.SystemHealth,
        meta: {
          title: '系统健康',
          keepAlive: true,
          roles: ['admin']
        }
      },
      {
        id: 24,
        path: 'logs',
        name: 'LogManagement',
        component: RoutesAlias.LogManagement,
        meta: {
          title: '日志管理',
          keepAlive: true,
          roles: ['admin']
        }
      }
    ]
  },
  {
    id: 3,
    path: '/score',
    name: 'Score',
    component: RoutesAlias.Home,
    meta: {
      title: '谱曲管理',
      icon: '&#xe7ae;',
      keepAlive: true,
      roles: ['admin']
    },
    children: [
      {
        id: 31,
        path: 'list',
        name: 'ScoreList',
        component: RoutesAlias.ScoreList,
        meta: {
          title: '谱曲列表',
          keepAlive: true
        }
      },
      {
        id: 32,
        path: 'detail/:id',
        name: 'ScoreDetail',
        component: RoutesAlias.ScoreDetail,
        meta: {
          title: '谱曲详情',
          isHide: true,
          keepAlive: true
        }
      },
      {
        id: 33,
        path: 'categories',
        name: 'ScoreCategories',
        component: RoutesAlias.CategoryManagement,
        meta: {
          title: '分类管理',
          keepAlive: true
        }
      }
    ]
  },
  {
    id: 4,
    path: '/admin',
    name: 'AdminManagement',
    component: RoutesAlias.Home,
    meta: {
      title: '管理员功能',
      icon: '&#xe7b9;',
      keepAlive: true,
      roles: ['admin']
    },
    children: [
      {
        id: 41,
        path: 'users',
        name: 'AdminUsers',
        component: RoutesAlias.AdminUserManagement,
        meta: {
          title: '用户管理',
          keepAlive: true
        }
      },
      {
        id: 42,
        path: 'resellers',
        name: 'AdminResellers',
        component: RoutesAlias.AdminResellerManagement,
        meta: {
          title: '代理商管理',
          keepAlive: true
        }
      },
      {
        id: 421,
        path: 'reseller-levels',
        name: 'AdminResellerLevels',
        component: RoutesAlias.AdminResellerLevels,
        meta: {
          title: '代理商级别管理',
          keepAlive: true
        }
      },
      {
        id: 43,
        path: 'licenses',
        name: 'AdminLicenses',
        component: RoutesAlias.AdminLicenseManagement,
        meta: {
          title: '卡密管理',
          keepAlive: true
        }
      },
      {
        id: 44,
        path: 'finance',
        name: 'AdminFinance',
        component: RoutesAlias.AdminFinanceManagement,
        meta: {
          title: '财务管理',
          keepAlive: true
        }
      }
    ]
  },
  {
    id: 5,
    path: '/reseller',
    name: 'ResellerManagement',
    component: RoutesAlias.Home,
    meta: {
      title: '代理商功能',
      icon: '&#xe816;',
      keepAlive: true,
      roles: ['reseller']
    },
    children: [
      {
        id: 51,
        path: 'dashboard',
        name: 'ResellerDashboard',
        component: RoutesAlias.ResellerDashboard,
        meta: {
          title: '控制台',
          keepAlive: true
        }
      },
      {
        id: 52,
        path: 'license-keys',
        name: 'ResellerLicenseKeys',
        component: RoutesAlias.ResellerLicenseKeys,
        meta: {
          title: '卡密管理',
          keepAlive: true
        }
      },
      {
        id: 53,
        path: 'sales',
        name: 'ResellerSales',
        component: RoutesAlias.ResellerSales,
        meta: {
          title: '销售统计',
          keepAlive: true
        }
      },
      {
        id: 54,
        path: 'commission',
        name: 'ResellerCommission',
        component: RoutesAlias.ResellerCommission,
        meta: {
          title: '佣金记录',
          keepAlive: true
        }
      },
      {
        id: 55,
        path: 'withdrawal',
        name: 'ResellerWithdrawal',
        component: RoutesAlias.ResellerWithdrawal,
        meta: {
          title: '提现管理',
          keepAlive: true
        }
      }
    ]
  },
  {
    id: 6,
    path: '/tickets',
    name: 'Tickets',
    component: RoutesAlias.Home,
    meta: {
      title: '工单系统',
      icon: '&#xe719;',
      keepAlive: true,
      roles: ['admin']
    },
    children: [
      {
        id: 61,
        path: 'list',
        name: 'TicketList',
        component: RoutesAlias.TicketList,
        meta: {
          title: '工单列表',
          keepAlive: true
        }
      },
      {
        id: 62,
        path: 'detail/:id',
        name: 'TicketDetail',
        component: RoutesAlias.TicketDetail,
        meta: {
          title: '工单详情',
          isHide: true,
          keepAlive: true
        }
      }
    ]
  }
  // 其他菜单已移除，文件保留用于参考
]
