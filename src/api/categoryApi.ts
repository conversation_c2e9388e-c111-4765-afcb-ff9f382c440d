import request from '@/utils/http'
import { CategoryDetailResponse, CategoryCreate, CategoryUpdate } from './model/categoryModel'

export class CategoryService {
  // 获取谱曲分类列表 (公开)
  static getCategories(
    params: {
      skip?: number
      limit?: number
      parent_id?: number
      only_active?: boolean
    } = {}
  ) {
    return request.get<CategoryDetailResponse[]>({
      url: '/api/v1/categories/',
      params
    })
  }

  // 获取特定分类详情 (公开)
  static getCategoryByIdOrSlug(categoryIdOrSlug: string) {
    return request.get<CategoryDetailResponse>({
      url: `/api/v1/categories/${categoryIdOrSlug}`
    })
  }

  // 创建分类 (管理员)
  static createCategory(params: CategoryCreate) {
    return request.post<CategoryDetailResponse>({
      url: '/api/v1/admin/categories/',
      data: params
    })
  }

  // 更新分类 (管理员)
  static updateCategory(categoryId: number, params: CategoryUpdate) {
    return request.put<CategoryDetailResponse>({
      url: `/api/v1/admin/categories/${categoryId}`,
      data: params
    })
  }

  // 删除分类 (管理员)
  static deleteCategory(categoryId: number) {
    return request.del<{ message: string }>({
      url: `/api/v1/admin/categories/${categoryId}`
    })
  }
}
