// src/api/__tests__/usersApi.spec.ts
import { describe, it, expect, vi } from 'vitest' // 或者 jest
import { AuthService } from '../usersApi'
import request from '@/utils/http' // 我们需要模拟这个模块
import type { AdminLoginPasswordRequest, AdminResellerLoginResponse } from '../model/authModel'

// 模拟 @/utils/http 模块
vi.mock('@/utils/http', () => ({
  default: {
    post: vi.fn(),
  },
}))

// 模拟 @/store/modules/user 模块，因为 http 拦截器会使用它
// 如果测试因找不到 store 而失败，我们将添加这个 mock
vi.mock('@/store/modules/user', () => ({
  useUserStore: vi.fn(() => ({
    accessToken: 'fake-token', // 提供一个假的 token 供 http 拦截器使用
  })),
}))

describe('AuthService', () => {
  describe('adminLogin', () => {
    it('should call request.post with correct URL and data for adminLogin and handle successful response', async () => {
      const loginCredentials: AdminLoginPasswordRequest = {
        username: 'admin',
        password: 'password123',
      }
      const mockSuccessResponse: AdminResellerLoginResponse = {
        access_token: 'mockAdminToken',
        refresh_token: 'mockRefreshToken',
        token_type: 'Bearer',
        expires_in: 3600,
        session_token: null,
        user_info: {
          id: 1,
          username: 'admin',
          role: 'admin' as const,
          email: '<EMAIL>',
          avatar_url: '',
          created_at: '2024-01-01T00:00:00Z',
          status: 'active' as const
        },
        license_info: null
      }

      // 配置 request.post 的模拟实现
      (request.post as vi.Mock).mockResolvedValue(mockSuccessResponse)

      const response = await AuthService.adminLogin(loginCredentials)

      // 验证 request.post 是否被正确调用
      expect(request.post).toHaveBeenCalledWith({
        url: '/api/admin/auth/login',
        data: loginCredentials,
      })

      // 验证响应是否符合预期
      expect(response).toEqual(mockSuccessResponse)
    })

    it('should handle API error for adminLogin', async () => {
      const loginCredentials: AdminLoginPasswordRequest = {
        username: 'admin',
        password: 'wrongpassword',
      }
      const mockErrorResponse = {
        response: { // 模拟 Axios 错误结构
          data: { msg: 'Invalid credentials' },
          status: 401,
        }
      }

      // 配置 request.post 的模拟实现以抛出错误
      (request.post as vi.Mock).mockRejectedValue(mockErrorResponse)

      try {
        await AuthService.adminLogin(loginCredentials)
      } catch (error: any) {
        // 验证 request.post 是否被正确调用
        expect(request.post).toHaveBeenCalledWith({
          url: '/api/admin/auth/login',
          data: loginCredentials,
        })
        // 验证错误是否被正确抛出和处理
        // 注意：AuthService 本身不处理错误，它会直接将 request.post 的 reject 往上抛
        // 因此我们在这里捕获并断言它与模拟的错误相同
        expect(error).toEqual(mockErrorResponse)
      }
    })
  })

  // resellerLogin 的测试将遵循类似的模式
  // ...
})