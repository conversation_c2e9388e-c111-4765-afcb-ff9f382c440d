import request from '@/utils/http'
import {
  LoginPasswordRequest,
  LoginLicenseRequest,
  LoginResponse,
  AdminLoginPasswordRequest,
  AdminResellerLoginResponse,
  ResellerLoginPasswordRequest,
  RegisterRequest,
  RegisterResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  LogoutRequest,
  PasswordResetRequestRequest,
  PasswordResetExecuteRequest,
  VerificationCodeSendRequest,
  HeartbeatRequest,
  ActivateLicenseAccountRequest,
  ActivateLicenseAccountResponse,
  Msg
} from './model/authModel'
import {
  UserMeResponse,
  UserProfileUpdate,
  UserPasswordUpdate,
  UserAvatarUpdateResponse,
  UserLicenseDetailsResponse,
  UserLicenseActivateRequest,
  UserLicenseActivateResponse,
  DeviceListResponse,
  DeviceUpdate,
  DeviceResponse,
  DeviceUnbindResponse,
  UserFavoriteResponse
} from './model/userModel'

export class AuthService {
  // 用户登录（用户名密码）
  static userLogin(params: LoginPasswordRequest) {
    return request.post<LoginResponse>({
      url: '/api/v1/auth/login',
      data: params
    })
  }

  // 用户登录（卡密）
  static userLicenseLogin(params: LoginLicenseRequest) {
    return request.post<LoginResponse>({
      url: '/api/v1/auth/login',
      data: params
    })
  }

  // 管理员登录
  static adminLogin(params: AdminLoginPasswordRequest) {
    return request.post<AdminResellerLoginResponse>({
      url: '/api/v1/admin/auth/login',
      data: params
    })
  }

  // 代理商登录
  static resellerLogin(params: ResellerLoginPasswordRequest) {
    return request.post<AdminResellerLoginResponse>({
      url: '/api/v1/reseller-auth/login',
      data: params
    })
  }

  // 用户注册
  static register(params: RegisterRequest) {
    return request.post<RegisterResponse>({
      url: '/api/v1/auth/register',
      data: params
    })
  }

  // 刷新访问令牌
  static refreshToken(params: RefreshTokenRequest) {
    return request.post<RefreshTokenResponse>({
      url: '/api/v1/auth/refresh-token',
      data: params
    })
  }

  // 用户登出
  static logout(params: LogoutRequest) {
    return request.post<Msg>({
      url: '/api/v1/auth/logout',
      data: params
    })
  }

  // 请求密码重置
  static requestPasswordReset(params: PasswordResetRequestRequest) {
    return request.post<Msg>({
      url: '/api/v1/auth/password/request-reset',
      data: params
    })
  }

  // 执行密码重置
  static resetPassword(params: PasswordResetExecuteRequest) {
    return request.post<Msg>({
      url: '/api/v1/auth/password/reset',
      data: params
    })
  }

  // 发送验证码
  static sendVerificationCode(params: VerificationCodeSendRequest) {
    return request.post<Msg>({
      url: '/api/v1/auth/verification-code/send',
      data: params
    })
  }

  // 会话心跳
  static heartbeat(params: HeartbeatRequest) {
    return request.post<Msg>({
      url: '/api/v1/auth/heartbeat',
      data: params
    })
  }

  // 激活卡密并创建/关联账户
  static activateLicenseAccount(params: ActivateLicenseAccountRequest) {
    return request.post<ActivateLicenseAccountResponse>({
      url: '/api/v1/auth/activate-license-account',
      data: params
    })
  }
}

export class UserService {
  // 获取当前用户信息
  static getUserInfo() {
    return request.get<UserMeResponse>({
      url: '/api/v1/users/me'
    })
  }

  // 更新当前用户信息
  static updateUserInfo(params: UserProfileUpdate) {
    return request.put<UserMeResponse>({
      url: '/api/v1/users/me',
      data: params
    })
  }

  // 修改当前用户密码
  static changePassword(params: UserPasswordUpdate) {
    return request.post<Msg>({
      url: '/api/v1/users/me/change-password',
      data: params
    })
  }

  // 更新用户头像
  static updateAvatar(file: File) {
    const formData = new FormData()
    formData.append('file', file)
    return request.put<UserAvatarUpdateResponse>({
      url: '/api/v1/users/me/avatar',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  // 获取我收藏的乐谱
  static getFavoriteScores(skip = 0, limit = 20) {
    return request.get<any[]>({
      url: '/api/v1/users/me/favorite-scores',
      params: { skip, limit }
    })
  }

  // 收藏乐谱
  static addScoreToFavorites(scoreId: number) {
    return request.post<Msg>({
      url: `/api/v1/users/me/scores/${scoreId}/favorite`
    })
  }

  // 取消收藏乐谱
  static removeScoreFromFavorites(scoreId: number) {
    return request.del<Msg>({
      url: `/api/v1/users/me/scores/${scoreId}/favorite`
    })
  }

  // 查看当前用户的卡密信息
  static getLicenseDetails() {
    return request.get<UserLicenseDetailsResponse>({
      url: '/api/v1/users/me/license'
    })
  }

  // 用户使用新卡密激活/续期
  static activateOrRenewLicense(params: UserLicenseActivateRequest) {
    return request.post<UserLicenseActivateResponse>({
      url: '/api/v1/users/me/license/activate',
      data: params
    })
  }

  // 获取当前用户绑定的设备列表
  static getUserDevices() {
    return request.get<DeviceListResponse>({
      url: '/api/v1/users/me/devices'
    })
  }

  // 修改用户指定绑定设备的别名
  static updateDeviceAlias(deviceId: string, params: DeviceUpdate) {
    return request.put<DeviceResponse>({
      url: `/api/v1/users/me/devices/${deviceId}`,
      data: params
    })
  }

  // 用户解绑指定的设备
  static unbindDevice(deviceId: string) {
    return request.del<DeviceUnbindResponse>({
      url: `/api/v1/users/me/devices/${deviceId}/unbind`
    })
  }

  // 获取当前用户收藏的谱曲列表
  static getUserFavorites(skip = 0, limit = 20) {
    return request.get<UserFavoriteResponse[]>({
      url: '/api/v1/users/me/favorites/',
      params: { skip, limit }
    })
  }
}
