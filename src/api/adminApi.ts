import request from '@/utils/http'
import {
  TicketCreate,
  TicketResponse,
  TicketDetailResponse,
  TicketListResponse,
  TicketMessageCreate,
  TicketMessageResponse,
  TicketUpdateByAdmin,
  WithdrawalRecordCreate,
  WithdrawalRecordResponse,
  WithdrawalRecordUpdateByAdmin,
  WithdrawalProcessRequest,
  UserLeaderboardEntry,
  UserBaseInfoForReseller,
  DashboardOverviewResponse,
  UserStatisticsResponse,
  RevenueStatisticsResponse,
  ScoreStatisticsResponse,
  TicketStatisticsResponse,
  SystemHealthResponse,
  SystemConfigResponse,
  SystemConfigUpdate,
  UserSessionLogResponse,
  UserSessionStatsResponse,
  CommissionRecordResponse,
  LicenseTypeResponse,
  BatchGenerateLicenseRequest,
  LicenseKeyResponse,
  CommentResponse,
  CommentUpdate,
  ResellerDetailResponse,
  ResellerStatusUpdate,
  ExportTaskStatusResponse,
  Msg,
  PaginatedResponse
} from './model/categoryModel'
import { UserResponse } from './model/userModel'
import { ScoreResponse, ScoreStatusUpdate } from './model/scoreModel'

// 管理员工单系统服务
export class AdminTicketService {
  // 获取工单列表
  static getTickets(
    params: {
      skip?: number
      limit?: number
      status?: string
      priority?: string
      category?: string
      user_id?: number
      assignee_user_id?: number
      search?: string
      created_after?: string
      created_before?: string
    } = {}
  ) {
    return request.get<PaginatedResponse<TicketResponse>>({
      url: '/api/v1/admin/tickets/',
      params
    })
  }

  // 获取工单详情
  static getTicketDetail(ticketId: number) {
    return request.get<TicketDetailResponse>({
      url: `/api/v1/admin/tickets/${ticketId}`
    })
  }

  // 管理员更新工单
  static updateTicket(ticketId: number, data: TicketUpdateByAdmin) {
    return request.put<TicketResponse>({
      url: `/api/v1/admin/tickets/${ticketId}`,
      data
    })
  }

  // 管理员回复工单
  static addTicketMessage(ticketId: number, data: TicketMessageCreate) {
    return request.post<TicketMessageResponse>({
      url: `/api/v1/admin/tickets/${ticketId}/messages`,
      data
    })
  }

  // 获取工单消息列表
  static getTicketMessages(ticketId: number, params: { skip?: number; limit?: number } = {}) {
    return request.get<TicketMessageResponse[]>({
      url: `/api/v1/admin/tickets/${ticketId}/messages`,
      params
    })
  }

  // 获取工单统计
  static getTicketStats(params: { days?: number } = {}) {
    return request.get<any>({
      url: '/api/v1/admin/tickets/stats',
      params
    })
  }
}

// 管理员用户管理服务
export class AdminUserService {
  // 获取用户列表 - 注意：此接口在API文档中不存在，使用通用用户接口
  static getUsers(
    params: {
      skip?: number
      limit?: number
      role?: string
      status?: string
      search?: string
      email_verified?: boolean
      created_after?: string
      created_before?: string
    } = {}
  ) {
    // 由于API文档中没有管理员用户列表接口，暂时返回空数据
    // 实际项目中需要后端提供相应的管理员用户列表接口
    return Promise.resolve({
      total: 0,
      items: [],
      page: 1,
      limit: params.limit || 20
    } as PaginatedResponse<UserResponse>)
  }

  // 创建新用户
  static createUser(data: {
    username: string
    email?: string
    password: string
    role?: string
    nickname?: string
    phone_number?: string
  }) {
    return request.post<UserResponse>({
      url: '/api/v1/admin/users/',
      data
    })
  }

  // 获取用户详情
  static getUserDetail(userId: number) {
    return request.get<UserResponse>({
      url: `/api/v1/admin/users/${userId}`
    })
  }

  // 更新用户信息
  static updateUser(userId: number, data: {
    username?: string
    email?: string
    nickname?: string
    phone_number?: string
    role?: string
    status?: string
    avatar_url?: string
  }) {
    return request.put<UserResponse>({
      url: `/api/v1/admin/users/${userId}`,
      data
    })
  }

  // 删除用户
  static deleteUser(userId: number) {
    return request.delete<Msg>({
      url: `/api/v1/admin/users/${userId}`
    })
  }

  // 获取用户上传排行榜
  static getUserUploadLeaderboard(params: { limit?: number } = {}) {
    return request.get<UserLeaderboardEntry[]>({
      url: '/api/v1/leaderboards/users/uploads',
      params
    })
  }
}

// 管理员谱曲管理服务
export class AdminScoreService {
  // 获取所有谱曲（包括待审核） - 注意：此接口在API文档中不存在，使用通用谱曲接口
  static getAllScores(
    params: {
      skip?: number
      limit?: number
      status?: string
      uploader_id?: number
      category_id?: number
      title?: string
      score_type?: string
      difficulty?: string
      is_premium_only?: boolean
      created_after?: string
      created_before?: string
    } = {}
  ) {
    // 使用通用谱曲接口，但添加管理员可见的状态筛选
    return request.get<ScorePaginatedResponse>({
      url: '/api/v1/scores/',
      params: {
        skip: params.skip,
        limit: params.limit,
        title: params.title,
        status: params.status as any,
        difficulty: params.difficulty as any,
        uploader_id: params.uploader_id,
        category_id: params.category_id,
        score_type: params.score_type as any,
        is_premium_only: params.is_premium_only
      }
    }).then(response => ({
      total: response.meta.total,
      items: response.data,
      page: response.meta.page,
      limit: response.meta.limit
    }))
  }

  // 获取谱曲详情
  static getScoreDetail(scoreId: number) {
    return request.get<ScoreResponse>({
      url: `/api/v1/admin/scores/${scoreId}`
    })
  }

  // 更新谱曲信息
  static updateScore(scoreId: number, data: any) {
    return request.put<ScoreResponse>({
      url: `/api/v1/admin/scores/${scoreId}`,
      data
    })
  }

  // 删除谱曲
  static deleteScore(scoreId: number) {
    return request.delete<Msg>({
      url: `/api/v1/admin/scores/${scoreId}`
    })
  }

  // 更新谱曲状态（审核）
  static updateScoreStatus(scoreId: number, data: ScoreStatusUpdate) {
    return request.put<ScoreResponse>({
      url: `/api/v1/admin/scores/${scoreId}/status`,
      data
    })
  }

  // 批量审核谱曲
  static batchUpdateScoreStatus(data: {
    score_ids: number[]
    status: string
    approval_remarks?: string
  }) {
    return request.post<Msg>({
      url: '/api/v1/admin/scores/batch-status',
      data
    })
  }

  // 获取谱曲统计信息
  static getScoreStats(params: { days?: number } = {}) {
    return request.get<any>({
      url: '/api/v1/admin/scores/stats',
      params
    })
  }
}

// 管理员卡密管理服务
export class AdminLicenseService {
  // 获取卡密列表 - 注意：此接口在API文档中不存在
  static getLicenses(
    params: {
      skip?: number
      limit?: number
      license_type_code?: string
      status?: string
      search?: string
    } = {}
  ) {
    // 由于API文档中没有卡密列表接口，暂时返回空数据
    return Promise.resolve({
      total: 0,
      items: [],
      page: 1,
      limit: params.limit || 20
    })
  }

  // 创建卡密
  static createLicense(data: any) {
    return request.post<any>({
      url: '/api/v1/admin/licenses/',
      data
    })
  }

  // 批量创建卡密
  static batchCreateLicenses(data: any) {
    return request.post<any>({
      url: '/api/v1/admin/licenses/batch',
      data
    })
  }

  // 更新卡密
  static updateLicense(licenseId: number, data: any) {
    return request.put<any>({
      url: `/api/v1/admin/licenses/${licenseId}`,
      data
    })
  }

  // 删除卡密
  static deleteLicense(licenseId: number) {
    return request.del<Msg>({
      url: `/api/v1/admin/licenses/${licenseId}`
    })
  }
}

// 代理管理服务
export class ResellerService {
  // 获取代理列表 - 注意：此接口在API文档中不存在
  static getResellers(
    params: {
      skip?: number
      limit?: number
      search?: string
    } = {}
  ) {
    // 由于API文档中没有代理列表接口，暂时返回空数据
    return Promise.resolve({
      total: 0,
      items: [],
      page: 1,
      limit: params.limit || 20
    })
  }

  // 创建提现记录
  static createWithdrawalRecord(params: WithdrawalRecordCreate) {
    return request.post<WithdrawalRecordResponse>({
      url: '/api/v1/reseller/withdrawals/',
      data: params
    })
  }

  // 获取提现记录列表 - 注意：此接口在API文档中不存在
  static getWithdrawalRecords(
    params: {
      skip?: number
      limit?: number
      status?: string
      reseller_user_id?: number
    } = {}
  ) {
    // 由于API文档中没有提现记录列表接口，暂时返回空数据
    return Promise.resolve({
      total: 0,
      items: [],
      page: 1,
      limit: params.limit || 20
    })
  }

  // 管理员更新提现记录
  static updateWithdrawalRecord(withdrawalId: number, params: WithdrawalRecordUpdateByAdmin) {
    return request.put<WithdrawalRecordResponse>({
      url: `/api/v1/admin/withdrawals/${withdrawalId}`,
      data: params
    })
  }

  // 处理提现请求
  static processWithdrawal(withdrawalId: number, params: WithdrawalProcessRequest) {
    return request.post<WithdrawalRecordResponse>({
      url: `/api/v1/admin/withdrawals/${withdrawalId}/process`,
      data: params
    })
  }
}

// 统计仪表盘服务
export class AdminDashboardService {
  // 获取系统整体概览数据 - 注意：此接口在API文档中不存在
  static getOverview(params: { time_range?: string } = {}) {
    // 由于API文档中没有dashboard接口，返回模拟数据
    return Promise.resolve({
      data: {
        user_stats: {
          total: 0,
          new_in_period: 0,
          active_in_period: 0
        },
        score_stats: {
          total: 0,
          new_in_period: 0,
          pending_review: 0
        },
        ticket_stats: {
          total: 0,
          pending: 0,
          new_in_period: 0
        },
        revenue_stats: {
          total_revenue: 0,
          period_revenue: 0
        }
      }
    } as { data: DashboardOverviewResponse })
  }

  // 获取用户统计详情
  static getUserStatistics(params: { time_range?: string; days?: number } = {}) {
    return request.get<UserStatisticsResponse>({
      url: '/api/v1/admin/dashboard/user-statistics',
      params
    })
  }

  // 获取收入统计详情
  static getRevenueStatistics(params: { time_range?: string; days?: number } = {}) {
    return request.get<RevenueStatisticsResponse>({
      url: '/api/v1/admin/dashboard/revenue-statistics',
      params
    })
  }

  // 获取谱曲统计详情
  static getScoreStatistics(params: { time_range?: string; days?: number } = {}) {
    return request.get<ScoreStatisticsResponse>({
      url: '/api/v1/admin/dashboard/score-statistics',
      params
    })
  }

  // 获取工单统计详情
  static getTicketStatistics(params: { time_range?: string; days?: number } = {}) {
    return request.get<TicketStatisticsResponse>({
      url: '/api/v1/admin/dashboard/ticket-statistics',
      params
    })
  }

  // 获取最近系统活动
  static getRecentActivities(params: { limit?: number } = {}) {
    return request.get<any[]>({
      url: '/api/v1/admin/dashboard/recent-activities',
      params
    })
  }
}

// 系统管理服务
export class AdminSystemService {
  // 系统健康检查
  static getHealth() {
    return request.get<SystemHealthResponse>({
      url: '/api/v1/admin/system/health'
    })
  }

  // 获取系统基本信息
  static getInfo() {
    return request.get<any>({
      url: '/api/v1/admin/system/info'
    })
  }

  // 获取系统配置
  static getConfig() {
    return request.get<SystemConfigResponse>({
      url: '/api/v1/admin/system/config'
    })
  }

  // 更新系统配置
  static updateConfig(data: SystemConfigUpdate) {
    return request.put<SystemConfigResponse>({
      url: '/api/v1/admin/system/config',
      data
    })
  }

  // 清理缓存
  static clearCache() {
    return request.post<Msg>({
      url: '/api/v1/admin/system/cache/clear'
    })
  }

  // 启用维护模式
  static enableMaintenance() {
    return request.post<Msg>({
      url: '/api/v1/admin/system/maintenance/enable'
    })
  }

  // 禁用维护模式
  static disableMaintenance() {
    return request.post<Msg>({
      url: '/api/v1/admin/system/maintenance/disable'
    })
  }

  // 导出用户数据
  static exportUsers(params: any = {}) {
    return request.post<{ task_id: string }>({
      url: '/api/v1/admin/system/export/users',
      data: params
    })
  }

  // 查询导出任务状态
  static getExportStatus(taskId: string) {
    return request.get<ExportTaskStatusResponse>({
      url: `/api/v1/admin/system/export/status/${taskId}`
    })
  }
}

// 日志管理服务
export class AdminLogService {
  // 获取用户会话日志 - 注意：此接口在API文档中不存在
  static getUserSessions(params: {
    skip?: number
    limit?: number
    user_id?: number
    device_id?: string
    start_date?: string
    end_date?: string
  } = {}) {
    // 由于API文档中没有日志接口，返回空数据
    return Promise.resolve({
      total: 0,
      items: [],
      page: 1,
      limit: params.limit || 20
    } as PaginatedResponse<UserSessionLogResponse>)
  }

  // 获取特定会话日志
  static getUserSession(logId: number) {
    return request.get<UserSessionLogResponse>({
      url: `/api/v1/admin/logs/user-sessions/${logId}`
    })
  }

  // 用户会话统计 - 注意：此接口在API文档中不存在
  static getUserSessionStats(params: { days?: number } = {}) {
    // 返回空统计数据
    return Promise.resolve({
      total_sessions: 0,
      active_sessions: 0,
      average_duration: 0,
      daily_stats: []
    } as UserSessionStatsResponse)
  }

  // 清理过期会话日志
  static cleanupUserSessions(params: { days_to_keep?: number } = {}) {
    return request.delete<Msg>({
      url: '/api/v1/admin/logs/user-sessions/cleanup',
      params
    })
  }

  // 获取系统日志 - 注意：此接口在API文档中不存在
  static getSystemLogs(params: {
    skip?: number
    limit?: number
    level?: string
    start_date?: string
    end_date?: string
  } = {}) {
    // 返回空数据
    return Promise.resolve({
      total: 0,
      items: [],
      page: 1,
      limit: params.limit || 20
    } as PaginatedResponse<any>)
  }

  // 获取审计日志 - 注意：此接口在API文档中不存在
  static getAuditLogs(params: {
    skip?: number
    limit?: number
    user_id?: number
    action?: string
    start_date?: string
    end_date?: string
  } = {}) {
    // 返回空数据
    return Promise.resolve({
      total: 0,
      items: [],
      page: 1,
      limit: params.limit || 20
    } as PaginatedResponse<any>)
  }
}

// 财务管理服务
export class AdminFinanceService {
  // 获取佣金记录
  static getCommissionRecords(params: {
    skip?: number
    limit?: number
    reseller_user_id?: number
    start_date?: string
    end_date?: string
    status?: string
  } = {}) {
    return request.get<PaginatedResponse<CommissionRecordResponse>>({
      url: '/api/v1/admin/finance/commission-records',
      params
    })
  }

  // 获取提现记录
  static getWithdrawalRecords(params: {
    skip?: number
    limit?: number
    reseller_user_id?: number
    status?: string
    start_date?: string
    end_date?: string
  } = {}) {
    return request.get<PaginatedResponse<WithdrawalRecordResponse>>({
      url: '/api/v1/admin/finance/withdrawal-records',
      params
    })
  }

  // 处理提现请求
  static processWithdrawalRecord(recordId: number, data: {
    status: string
    admin_notes?: string
  }) {
    return request.put<WithdrawalRecordResponse>({
      url: `/api/v1/admin/finance/withdrawal-records/${recordId}`,
      data
    })
  }
}

// 更新后的卡密管理服务
export class AdminLicenseKeyService {
  // 获取卡密类型
  static getLicenseTypes() {
    return request.get<LicenseTypeResponse[]>({
      url: '/api/v1/admin/license-keys/types/'
    })
  }

  // 批量生成卡密
  static batchGenerate(data: BatchGenerateLicenseRequest) {
    return request.post<{ task_id: string; message: string }>({
      url: '/api/v1/admin/license-keys/batch-generate/',
      data
    })
  }

  // 获取卡密列表
  static getLicenseKeys(params: {
    skip?: number
    limit?: number
    license_type_id?: number
    status?: string
    reseller_user_id?: number
    search?: string
  } = {}) {
    return request.get<PaginatedResponse<LicenseKeyResponse>>({
      url: '/api/v1/admin/license-keys/',
      params
    })
  }

  // 获取卡密详情
  static getLicenseKey(licenseKeyId: number) {
    return request.get<LicenseKeyResponse>({
      url: `/api/v1/admin/license-keys/${licenseKeyId}`
    })
  }

  // 更新卡密信息
  static updateLicenseKey(licenseKeyId: number, data: any) {
    return request.put<LicenseKeyResponse>({
      url: `/api/v1/admin/license-keys/${licenseKeyId}`,
      data
    })
  }

  // 删除卡密
  static deleteLicenseKey(licenseKeyId: number) {
    return request.delete<Msg>({
      url: `/api/v1/admin/license-keys/${licenseKeyId}`
    })
  }
}

// 更新后的代理管理服务
export class AdminResellerService {
  // 获取代理列表
  static getResellers(params: {
    skip?: number
    limit?: number
    search?: string
    status?: string
    level_id?: number
  } = {}) {
    return request.get<PaginatedResponse<ResellerDetailResponse>>({
      url: '/api/v1/admin/resellers/',
      params
    })
  }

  // 设置用户为代理
  static createReseller(data: {
    user_id: number
    commission_rate?: number
    level_id?: number
  }) {
    return request.post<ResellerDetailResponse>({
      url: '/api/v1/admin/resellers/',
      data
    })
  }

  // 获取代理详情
  static getResellerDetail(resellerUserId: number) {
    return request.get<ResellerDetailResponse>({
      url: `/api/v1/admin/resellers/${resellerUserId}`
    })
  }

  // 更新代理信息
  static updateReseller(resellerUserId: number, data: {
    commission_rate?: number
    level_id?: number
    status?: string
  }) {
    return request.put<ResellerDetailResponse>({
      url: `/api/v1/admin/resellers/${resellerUserId}`,
      data
    })
  }

  // 更新代理状态
  static updateResellerStatus(resellerUserId: number, data: ResellerStatusUpdate) {
    return request.patch<ResellerDetailResponse>({
      url: `/api/v1/admin/resellers/${resellerUserId}/status`,
      data
    })
  }
}

// 评论管理服务
export class AdminCommentService {
  // 获取评论列表
  static getComments(params: {
    skip?: number
    limit?: number
    score_id?: number
    user_id?: number
    is_approved?: boolean
    search?: string
  } = {}) {
    return request.get<PaginatedResponse<CommentResponse>>({
      url: '/api/v1/admin/comments/',
      params
    })
  }

  // 获取评论详情
  static getComment(commentId: number) {
    return request.get<CommentResponse>({
      url: `/api/v1/admin/comments/${commentId}`
    })
  }

  // 更新评论
  static updateComment(commentId: number, data: CommentUpdate) {
    return request.put<CommentResponse>({
      url: `/api/v1/admin/comments/${commentId}`,
      data
    })
  }

  // 删除评论
  static deleteComment(commentId: number) {
    return request.delete<Msg>({
      url: `/api/v1/admin/comments/${commentId}`
    })
  }

  // 批量审核评论
  static batchApproveComments(data: {
    comment_ids: number[]
    is_approved: boolean
  }) {
    return request.post<Msg>({
      url: '/api/v1/admin/comments/batch-approve',
      data
    })
  }
}
