// 分类相关数据模型

// 分类详情响应
export interface CategoryDetailResponse {
  id: number
  name: string
  slug?: string
  description?: string
  parent_id?: number
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
  children?: CategoryDetailResponse[]
  parent?: CategoryDetailResponse
  score_count?: number
}

// 分类创建请求
export interface CategoryCreate {
  name: string
  slug?: string
  description?: string
  parent_id?: number
  sort_order?: number
  is_active?: boolean
}

// 分类更新请求
export interface CategoryUpdate {
  name?: string
  slug?: string
  description?: string
  parent_id?: number
  sort_order?: number
  is_active?: boolean
}

// 排行榜时间范围枚举
export enum TimeRange {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  ALL_TIME = 'all_time'
}

// 工单优先级枚举
export enum TicketPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

// 工单状态枚举
export enum TicketStatus {
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  AWAITING_REPLY = 'awaiting_reply',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
  REOPENED = 'reopened'
}

// 工单创建请求
export interface TicketCreate {
  title: string
  description: string
  priority?: TicketPriority
  category?: string
}

// 工单响应
export interface TicketResponse {
  id: number
  ticket_number: string
  user_id: number
  title: string
  description: string
  priority: TicketPriority
  category?: string
  status: TicketStatus
  assignee_user_id?: number
  created_at: string
  updated_at: string
  resolved_at?: string
  closed_at?: string
  last_reply_at?: string
  user?: UserResponse
  assignee?: UserResponse
  latest_message_summary?: TicketMessageNestedResponse
}

// 工单详情响应
export interface TicketDetailResponse extends TicketResponse {
  messages: TicketMessageResponse[]
}

// 工单列表响应
export interface TicketListResponse {
  total: number
  items: TicketResponse[]
  page?: number
  limit?: number
}

// 工单消息创建请求
export interface TicketMessageCreate {
  content: string
  is_internal?: boolean
}

// 工单消息响应
export interface TicketMessageResponse {
  id: number
  ticket_id: number
  sender_id: number
  content: string
  is_internal: boolean
  created_at: string
  updated_at: string
  sender?: UserResponse
}

// 工单消息嵌套响应
export interface TicketMessageNestedResponse {
  id: number
  sender_id: number
  content: string
  created_at: string
  is_internal: boolean
}

// 管理员工单更新请求
export interface TicketUpdateByAdmin {
  status?: TicketStatus
  priority?: TicketPriority
  assignee_user_id?: number
  category?: string
}

// 用户响应
export interface UserResponse {
  id: number
  username: string
  email?: string
  phone_number?: string
  nickname?: string
  avatar_url?: string
  role: string
  status: string
  email_verified_at?: string
  created_at: string
}

// 提现状态枚举
export enum WithdrawalStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// 提现记录创建请求
export interface WithdrawalRecordCreate {
  amount: number | string
  payment_method: string
  payment_account: string
  remarks?: string
}

// 提现记录响应
export interface WithdrawalRecordResponse {
  id: number
  reseller_user_id: number
  reseller_username?: string
  amount: number | string
  status: WithdrawalStatus
  payment_method?: string
  payment_account?: string
  remarks?: string
  admin_notes?: string
  processed_at?: string
  created_at: string
  updated_at: string
}

// 提现记录管理员更新请求
export interface WithdrawalRecordUpdateByAdmin {
  status: WithdrawalStatus
  admin_notes?: string
}

// 提现处理请求
export interface WithdrawalProcessRequest {
  status: WithdrawalStatus
  remarks?: string
}

// 用户排行榜条目
export interface UserLeaderboardEntry {
  user: MinimalUserResponse
  upload_count: number
}

// 最小用户响应
export interface MinimalUserResponse {
  id: number
  username: string
  nickname?: string
  avatar_url?: string
}

// 代理用户基础信息
export interface UserBaseInfoForReseller {
  id: number
  username: string
  nickname?: string
  email?: string
  role: UserRole
}

// 用户角色枚举
export enum UserRole {
  ADMIN = 'admin',
  RESELLER = 'reseller',
  USER = 'user'
}

// 统计仪表盘相关模型
export interface DashboardOverviewResponse {
  user_stats: UserStatsBase
  score_stats: ScoreStatsBase
  ticket_stats: TicketStatsBase
  revenue_stats: RevenueStatsBase
  recent_activities: RecentActivity[]
}

export interface UserStatsBase {
  total: number
  new_in_period: number
  active_in_period: number
}

export interface ScoreStatsBase {
  total: number
  pending_review: number
  new_in_period: number
}

export interface TicketStatsBase {
  total: number
  pending: number
  new_in_period: number
}

export interface RevenueStatsBase {
  total_revenue: number
  period_revenue: number
  commission_paid: number
}

export interface RecentActivity {
  id: number
  type: string
  description: string
  user_id?: number
  username?: string
  created_at: string
}

export interface DailyDataPoint {
  date: string
  value: number
}

// 用户统计详细响应
export interface UserStatisticsResponse {
  role_distribution: Record<string, number>
  status_distribution: Record<string, number>
  source_distribution: Record<string, number>
  daily_registrations: DailyDataPoint[]
  period_days: number
}

// 收入统计详细响应
export interface RevenueStatisticsResponse {
  daily_revenue: DailyDataPoint[]
  commission_distribution: Record<string, number>
  payment_method_distribution: Record<string, number>
  period_days: number
  total_revenue: number
  total_commission: number
}

// 谱曲统计详细响应
export interface ScoreStatisticsResponse {
  status_distribution: Record<string, number>
  type_distribution: Record<string, number>
  category_distribution: Record<string, number>
  daily_uploads: DailyDataPoint[]
  period_days: number
}

// 工单统计详细响应
export interface TicketStatisticsResponse {
  status_distribution: Record<string, number>
  priority_distribution: Record<string, number>
  category_distribution: Record<string, number>
  daily_tickets: DailyDataPoint[]
  avg_response_time: number
  avg_resolution_time: number
  period_days: number
}

// 系统健康状态响应
export interface SystemHealthResponse {
  database_status: string
  redis_status?: string
  disk_usage: Record<string, any>
  memory_usage: Record<string, any>
  cpu_usage: number
  uptime: string
  version: string
}

// 系统配置响应
export interface SystemConfigResponse {
  [key: string]: any
}

// 系统配置更新请求
export interface SystemConfigUpdate {
  [key: string]: any
}

// 用户会话日志响应
export interface UserSessionLogResponse {
  id: number
  user_id: number
  username?: string
  device_id?: string
  ip_address?: string
  session_start_at: string
  session_end_at?: string
  duration_seconds?: number
  created_at: string
  updated_at: string
}

// 用户会话统计响应
export interface UserSessionStatsResponse {
  total_sessions: number
  active_sessions: number
  avg_session_duration: number
  daily_sessions: DailyDataPoint[]
  device_distribution: Record<string, number>
  period_days: number
}

// 佣金记录响应
export interface CommissionRecordResponse {
  id: number
  reseller_user_id: number
  reseller_username?: string
  license_key_id: number
  license_key_string: string
  commission_amount: number
  commission_rate: number
  sale_amount: number
  created_at: string
  status: string
}

// 卡密类型响应
export interface LicenseTypeResponse {
  id: number
  code: string
  name: string
  description?: string
  duration_days?: number
  max_concurrent_devices: number
  price: number
  commission_rate: number
  is_active: boolean
  created_at: string
  updated_at: string
}

// 批量生成卡密请求
export interface BatchGenerateLicenseRequest {
  license_type_id: number
  quantity: number
  prefix?: string
  reseller_user_id?: number
}

// 卡密响应
export interface LicenseKeyResponse {
  id: number
  key_string: string
  license_type_id: number
  license_type_name: string
  reseller_user_id?: number
  reseller_username?: string
  user_id?: number
  username?: string
  status: string
  activated_at?: string
  expires_at?: string
  created_at: string
  updated_at: string
}

// 评论管理相关模型
export interface CommentResponse {
  id: number
  score_id: number
  user_id: number
  parent_comment_id?: number
  content: string
  is_approved: boolean
  created_at: string
  updated_at: string
  user?: UserResponse
  score?: {
    id: number
    title: string
  }
  replies_count?: number
}

// 评论更新请求
export interface CommentUpdate {
  content?: string
  is_approved?: boolean
}

// 代理详情响应
export interface ResellerDetailResponse {
  id: number
  user_id: number
  username: string
  nickname?: string
  email?: string
  level_id?: number
  level_name?: string
  commission_rate: number
  total_sales: number
  total_commission: number
  available_balance: number
  status: string
  created_at: string
  updated_at: string
  recent_sales?: CommissionRecordResponse[]
}

// 代理状态更新请求
export interface ResellerStatusUpdate {
  status: string
  commission_rate?: number
  level_id?: number
}

// 导出任务状态响应
export interface ExportTaskStatusResponse {
  task_id: string
  status: string
  progress: number
  file_url?: string
  error_message?: string
  created_at: string
  completed_at?: string
}

// 通用消息响应
export interface Msg {
  message: string
}

// 分页响应基础接口
export interface PaginatedResponse<T> {
  total: number
  items: T[]
  page?: number
  limit?: number
}
