// 谱曲相关数据模型

// 谱曲类型枚举
export enum ScoreType {
  TXT = 'TXT',
  MIDI_REF = 'MIDI_REF',
  EXTERNAL_LINK = 'EXTERNAL_LINK'
}

// 谱曲难度枚举
export enum ScoreDifficulty {
  BEGINNER = 'beginner',
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
  EXPERT = 'expert'
}

// 谱曲状态枚举
export enum ScoreStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  PRIVATE = 'private',
  DRAFT = 'draft'
}

// MIDI存储类型枚举
export enum MidiStorageType {
  LOCAL = 'local',
  CLOUD = 'cloud',
  URL = 'url'
}

// 谱曲创建请求
export interface ScoreCreate {
  title: string
  description?: string
  category_id: number
  type: ScoreType
  txt_content?: string
  midi_storage_type?: MidiStorageType
  midi_path_or_url?: string
  original_midi_filename?: string
  external_url?: string
  difficulty?: ScoreDifficulty
  tags?: string[]
  cover_image_url?: string
  duration_seconds?: number
  is_premium_only?: boolean
  status?: ScoreStatus
  uploader_user_id: number
}

// 谱曲更新请求
export interface ScoreUpdate {
  title?: string
  description?: string
  category_id?: number
  type?: ScoreType
  txt_content?: string
  midi_storage_type?: MidiStorageType
  midi_path_or_url?: string
  original_midi_filename?: string
  external_url?: string
  difficulty?: ScoreDifficulty
  tags?: string[]
  cover_image_url?: string
  duration_seconds?: number
  is_premium_only?: boolean
  status?: ScoreStatus
}

// 谱曲响应
export interface ScoreResponse {
  id: number
  title: string
  description?: string
  category_id: number
  type: ScoreType
  txt_content?: string
  midi_storage_type?: MidiStorageType
  midi_path_or_url?: string
  original_midi_filename?: string
  external_url?: string
  difficulty?: ScoreDifficulty
  tags?: string[]
  cover_image_url?: string
  duration_seconds?: number
  is_premium_only: boolean
  status: ScoreStatus
  uploader_user_id: number
  view_count: number
  download_count: number
  favorite_count: number
  comment_count: number
  average_rating: number | string
  rating_count: number
  approved_by_user_id?: number
  approved_at?: string
  approval_remarks?: string
  created_at: string
  updated_at: string
  uploader?: UserNestedResponse
  approver?: UserNestedResponse
  category?: CategoryNestedResponse
}

// 谱曲详情响应
export type ScoreDetailResponse = ScoreResponse

// 谱曲摘要响应
export interface ScoreSummaryResponse {
  id: number
  title: string
  description_snippet?: string
  uploader?: UserNestedResponse
  type: ScoreType
  difficulty?: ScoreDifficulty
  view_count: number
  favorite_count: number
  average_rating: number | string
  comment_count: number
  cover_image_url?: string
  is_premium_only: boolean
  created_at: string
  tags?: string[]
}

// 谱曲分页响应
export interface ScorePaginatedResponse {
  meta: PageMeta
  data: ScoreSummaryResponse[]
}

// 分页元数据
export interface PageMeta {
  total: number
  page: number
  limit: number
  total_pages: number
}

// 用户嵌套响应
export interface UserNestedResponse {
  id: number
  username: string
  nickname?: string
  avatar_url?: string
}

// 分类嵌套响应
export interface CategoryNestedResponse {
  id: number
  name: string
  slug?: string
}

// 谱曲收藏状态响应
export interface ScoreIsFavoritedResponse {
  score_id: number
  is_favorited: boolean
}

// 谱曲评分创建请求
export interface UserScoreRatingCreate {
  user_id: number
  score_id: number
  rating: number // 1-5
  comment?: string
}

// 谱曲评分响应
export interface UserScoreRatingResponse {
  user_id: number
  score_id: number
  rating: number
  comment?: string
  created_at: string
  updated_at: string
  user?: UserNestedResponse
  score?: ScoreNestedResponse
}

// 谱曲评分摘要响应
export interface ScoreRatingSummaryResponse {
  score_id: number
  average_rating?: number
  total_ratings: number
}

// 谱曲评论创建请求
export interface ScoreCommentCreate {
  content: string
  parent_comment_id?: number
}

// 谱曲评论更新请求
export interface ScoreCommentUpdate {
  content: string
}

// 谱曲评论响应
export interface ScoreCommentResponse {
  id: number
  score_id: number
  user_id: number
  content: string
  parent_comment_id?: number
  created_at: string
  updated_at: string
  user?: UserNestedResponse
  replies?: ScoreCommentResponse[]
}

// 谱曲嵌套响应
export interface ScoreNestedResponse {
  id: number
  title: string
  cover_image_url?: string
}

// 谱曲状态更新请求
export interface ScoreStatusUpdate {
  status: ScoreStatus
  approval_remarks?: string
}
