// 用户相关数据模型

// 用户详细信息响应
export interface UserMeResponse {
  id: number
  username: string
  email?: string
  phone_number?: string
  nickname?: string
  avatar_url?: string
  role: string
  status: string
  email_verified_at?: string
  created_at: string
  license_info?: UserLicenseInfo
}

// 用户卡密信息
export interface UserLicenseInfo {
  key_string: string
  license_type_code: string
  license_type_name: string
  activated_at?: string
  expires_at?: string
}

// 用户资料更新
export interface UserProfileUpdate {
  username?: string
  email?: string
  nickname?: string
  avatar_url?: string
}

// 用户密码更新
export interface UserPasswordUpdate {
  current_password: string
  new_password: string
}

// 用户头像更新响应
export interface UserAvatarUpdateResponse {
  avatar_url: string
}

// 用户卡密详情响应
export interface UserLicenseDetailsResponse {
  active_license?: ActiveLicenseInfo
  history: HistoricalLicenseInfo[]
}

// 激活的卡密信息
export interface ActiveLicenseInfo {
  key_string: string
  license_type_code: string
  license_type_name: string
  activated_at: string
  expires_at?: string
  remaining_days?: number
  is_expired: boolean
}

// 历史卡密信息
export interface HistoricalLicenseInfo {
  key_string: string
  license_type_code: string
  license_type_name: string
  activated_at: string
  expires_at?: string
  deactivated_at?: string
  status: string
}

// 用户卡密激活请求
export interface UserLicenseActivateRequest {
  key_string: string
}

// 用户卡密激活响应
export interface UserLicenseActivateResponse {
  message: string
  license_info: LicenseInfoBase
}

// 基础卡密信息
export interface LicenseInfoBase {
  key_string: string
  license_type_code: string
  license_type_name: string
  activated_at?: string
  expires_at?: string
}

// 设备列表响应
export interface DeviceListResponse {
  devices: DeviceResponse[]
  total_devices: number
  max_devices: number
}

// 设备信息
export interface DeviceResponse {
  device_id: string
  device_name?: string
  device_alias?: string
  device_type?: string
  last_login_at?: string
  created_at: string
  is_current: boolean
}

// 设备更新
export interface DeviceUpdate {
  device_alias?: string
}

// 设备解绑响应
export interface DeviceUnbindResponse {
  message: string
  penalty_applied?: boolean
  penalty_details?: string
}

// 用户收藏响应
export interface UserFavoriteResponse {
  user_id: number
  score_id: number
  created_at: string
  user?: UserResponse
  score?: ScoreNestedResponse
}

// 用户响应（嵌套）
export interface UserResponse {
  id: number
  username: string
  email?: string
  phone_number?: string
  nickname?: string
  avatar_url?: string
  role: string
  status: string
  email_verified_at?: string
  created_at: string
}

// 谱曲响应（嵌套）
export interface ScoreNestedResponse {
  id: number
  title: string
  cover_image_url?: string
}

// 用户角色枚举
export enum UserRole {
  ADMIN = 'admin',
  RESELLER = 'reseller',
  USER = 'user'
}

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  BANNED = 'banned',
  PENDING_VERIFICATION = 'pending_verification'
}
