// 管理员相关的数据模型定义

import { UserInfo } from './authModel'

// 分页响应基础结构
export interface PaginatedResponse<T> {
  total: number
  items: T[]
  page?: number
  limit?: number
}

// 分页元数据
export interface PaginationMeta {
  page: number
  limit: number
  total_items: number
  total_pages: number
}

// 带分页元数据的响应
export interface PaginatedDataResponse<T> {
  meta: PaginationMeta
  data: T[]
}

// 通用消息响应
export interface Msg {
  message: string
}

// 工单相关模型
export interface TicketCreate {
  title: string
  description: string
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  category?: string
}

export interface TicketResponse {
  id: number
  ticket_number: string
  title: string
  description: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  category?: string
  user_id: number
  status: 'open' | 'in_progress' | 'awaiting_reply' | 'resolved' | 'closed' | 'reopened'
  assignee_user_id?: number
  created_at: string
  updated_at: string
  resolved_at?: string
  closed_at?: string
  last_reply_at?: string
  user?: UserResponse
  assignee?: UserResponse
  latest_message_summary?: TicketMessageNestedResponse
}

export interface TicketDetailResponse extends TicketResponse {
  messages: TicketMessageResponse[]
}

export interface TicketListResponse {
  total: number
  items: TicketResponse[]
  page?: number
  limit?: number
}

export interface TicketMessageCreate {
  content: string
  is_internal?: boolean
}

export interface TicketMessageResponse {
  id: number
  ticket_id: number
  sender_id: number
  content: string
  is_internal: boolean
  created_at: string
  updated_at: string
  sender?: UserResponse
}

export interface TicketMessageNestedResponse {
  id: number
  sender_id: number
  content: string
  created_at: string
  is_internal: boolean
}

export interface TicketUpdateByAdmin {
  status?: 'open' | 'in_progress' | 'awaiting_reply' | 'resolved' | 'closed' | 'reopened'
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  assignee_user_id?: number
  category?: string
}

// 用户相关模型
export interface UserResponse {
  id: number
  username: string
  email?: string
  phone_number?: string
  nickname?: string
  avatar_url?: string
  role: string
  status: string
  email_verified_at?: string
  created_at: string
}

export interface UserCreate {
  username: string
  email?: string
  password: string
  role?: string
  nickname?: string
  phone_number?: string
}

export interface UserUpdate {
  username?: string
  email?: string
  nickname?: string
  phone_number?: string
  role?: string
  status?: string
  avatar_url?: string
}

// 代理相关模型
export interface ResellerDetailResponse {
  user_id: number
  commission_rate: number
  level_id?: number
  status: string
  created_at: string
  updated_at: string
  user?: UserResponse
  level?: ResellerLevelResponse
  total_sales?: number
  total_commission?: number
}

export interface ResellerLevelResponse {
  id: number
  name: string
  commission_rate: number
  min_sales_threshold?: number
  description?: string
  is_active: boolean
  created_at: string
}

export interface ResellerStatusUpdate {
  status: string
  remarks?: string
}

// 提现相关模型
export interface WithdrawalRecordCreate {
  amount: number | string
  payment_method: string
  payment_account: string
  remarks?: string
}

export interface WithdrawalRecordResponse {
  id: number
  reseller_user_id: number
  reseller_username?: string
  amount: number | string
  status: 'pending' | 'approved' | 'rejected' | 'completed' | 'failed'
  payment_method?: string
  payment_account?: string
  remarks?: string
  admin_notes?: string
  processed_at?: string
  created_at: string
  updated_at: string
}

export interface WithdrawalRecordUpdateByAdmin {
  status: 'pending' | 'approved' | 'rejected' | 'completed' | 'failed'
  admin_notes?: string
}

export interface WithdrawalProcessRequest {
  status: 'approved' | 'rejected'
  remarks?: string
}

// 卡密相关模型
export interface LicenseTypeResponse {
  id: number
  code: string
  name: string
  duration_days?: number
  max_concurrent_devices: number
  price?: number
  description?: string
  is_active: boolean
  created_at: string
}

export interface BatchGenerateLicenseRequest {
  license_type_id: number
  quantity: number
  reseller_user_id?: number
  expires_at?: string
  notes?: string
}

export interface LicenseKeyResponse {
  id: number
  key_string: string
  license_type_id: number
  license_type?: LicenseTypeResponse
  reseller_user_id?: number
  reseller?: UserResponse
  user_id?: number
  user?: UserResponse
  status: 'unused' | 'used' | 'expired' | 'disabled'
  activated_at?: string
  expires_at?: string
  created_at: string
  updated_at: string
}

// 财务相关模型
export interface CommissionRecordResponse {
  id: number
  reseller_user_id: number
  reseller?: UserResponse
  license_key_id: number
  license_key?: LicenseKeyResponse
  sale_amount: number
  commission_rate: number
  commission_amount: number
  status: string
  created_at: string
}

// 统计相关模型
export interface DailyDataPoint {
  date: string
  count: number
}

export interface UserStatsBase {
  total: number
  new_in_period: number
  active_in_period: number
}

export interface ScoreStatsBase {
  total: number
  new_in_period: number
  pending_review: number
}

export interface TicketStatsBase {
  total: number
  pending: number
  new_in_period: number
}

export interface RevenueStatsBase {
  total_revenue: number
  period_revenue: number
}

export interface DashboardOverviewResponse {
  user_stats: UserStatsBase
  score_stats: ScoreStatsBase
  ticket_stats: TicketStatsBase
  revenue_stats: RevenueStatsBase
}

export interface UserStatisticsResponse {
  role_distribution: Record<string, number>
  status_distribution: Record<string, number>
  source_distribution: Record<string, number>
  daily_registrations: DailyDataPoint[]
  period_days: number
}

export interface RevenueStatisticsResponse {
  total_revenue: number
  period_revenue: number
  daily_revenue: DailyDataPoint[]
  commission_stats: Record<string, number>
  period_days: number
}

export interface ScoreStatisticsResponse {
  status_distribution: Record<string, number>
  type_distribution: Record<string, number>
  difficulty_distribution: Record<string, number>
  daily_uploads: DailyDataPoint[]
  period_days: number
}

export interface TicketStatisticsResponse {
  status_distribution: Record<string, number>
  priority_distribution: Record<string, number>
  category_distribution: Record<string, number>
  daily_tickets: DailyDataPoint[]
  avg_response_time: number
  avg_resolution_time: number
  period_days: number
}

// 系统相关模型
export interface SystemHealthResponse {
  database_status: string
  redis_status?: string
  disk_usage: Record<string, any>
  memory_usage: Record<string, any>
  cpu_usage: number
  uptime: string
  version: string
}

export interface SystemConfigResponse {
  [key: string]: any
}

export interface SystemConfigUpdate {
  [key: string]: any
}

// 日志相关模型
export interface UserSessionLogResponse {
  id: number
  user_id: number
  username?: string
  device_id?: string
  ip_address?: string
  session_start_at: string
  session_end_at?: string
  duration_seconds?: number
  created_at: string
  updated_at: string
}

export interface UserSessionStatsResponse {
  total_sessions: number
  active_sessions: number
  average_duration: number
  daily_stats: DailyDataPoint[]
}

// 导出任务相关模型
export interface ExportTaskStatusResponse {
  task_id: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress?: number
  download_url?: string
  error_message?: string
  created_at: string
  completed_at?: string
}

// 评论相关模型
export interface CommentResponse {
  id: number
  score_id: number
  user_id: number
  parent_comment_id?: number
  content: string
  is_approved: boolean
  created_at: string
  updated_at: string
  user?: UserResponse
  replies_count?: number
}

export interface CommentUpdate {
  content?: string
  is_approved?: boolean
}

// 用户排行榜
export interface MinimalUserResponse {
  id: number
  username: string
  nickname?: string
  avatar_url?: string
}

export interface UserLeaderboardEntry {
  user: MinimalUserResponse
  upload_count: number
}

// 代理基础信息
export interface UserBaseInfoForReseller {
  id: number
  username: string
  nickname?: string
  email?: string
  role: 'admin' | 'reseller' | 'user'
}
