// 认证相关数据模型

// 登录请求 - 用户名密码登录
export interface LoginPasswordRequest {
  username: string
  password: string
}

// 登录请求 - 卡密登录
export interface LoginLicenseRequest {
  key_string: string
}

// 登录响应
export interface LoginResponse {
  access_token: string
  refresh_token: string
  session_token: string
  token_type: string
  expires_in: number
  user_info: UserInfo
  license_info?: LicenseInfo
  device_status: string
}

// 用户信息
export interface UserInfo {
  id: number
  username: string
  email?: string
  phone_number?: string
  nickname?: string
  avatar_url?: string
  max_concurrent_devices?: number
  permanent_license_unbind_count_current_month?: number
  role: 'admin' | 'reseller' | 'user'
  status: 'active' | 'inactive' | 'banned' | 'pending_verification'
  email_verified_at?: string
  created_at: string
}

// 卡密信息
export interface LicenseInfo {
  id: number
  key_string: string
  license_type_code: string
  license_type_name: string
  activated_at?: string
  expires_at?: string
  status: string
}

// 注册请求
export interface RegisterRequest {
  username: string
  password: string
  email?: string
  phone_number?: string
  nickname?: string
  verification_code?: string
}

// 注册响应
export interface RegisterResponse {
  message: string
  user: UserInfo
}

// 刷新令牌请求
export interface RefreshTokenRequest {
  refresh_token: string
}

// 刷新令牌响应
export interface RefreshTokenResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
}

// 登出请求
export interface LogoutRequest {
  refresh_token?: string
}

// 密码重置请求
export interface PasswordResetRequestRequest {
  email: string
}

// 密码重置执行
export interface PasswordResetExecuteRequest {
  email: string
  reset_token?: string
  verification_code?: string
  new_password: string
}

// 验证码发送请求
export interface VerificationCodeSendRequest {
  type: 'email' | 'sms'
  recipient: string
  purpose: 'register' | 'login' | 'password_reset' | 'action_confirm'
}

// 心跳请求
export interface HeartbeatRequest {
  device_id?: string
}

// 激活卡密账户请求
export interface ActivateLicenseAccountRequest {
  key_string: string
  username?: string
  password?: string
  email?: string
  phone_number?: string
  nickname?: string
}

// 激活卡密账户响应
export interface ActivateLicenseAccountResponse {
  message: string
  user_info: UserInfo
  license_info: LicenseInfo
  access_token: string
  refresh_token: string
  session_token: string
  token_type: string
  expires_in: number
}

// 通用消息响应
export interface Msg {
  message: string
}

// 管理员登录请求 - 用户名密码登录
export interface AdminLoginPasswordRequest {
  username: string
  password: string
}

// 代理商登录请求 - 用户名密码登录
export interface ResellerLoginPasswordRequest {
  username: string
  password: string
}

// 管理员和代理商登录响应
export interface AdminResellerLoginResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  session_token?: string | null
  user_info: UserInfo // API返回的是 user_info 字段
  license_info?: any | null
}
