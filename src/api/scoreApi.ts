import request from '@/utils/http'
import {
  ScoreCreate,
  ScoreUpdate,
  ScoreResponse,
  ScoreDetailResponse,
  ScorePaginatedResponse,
  ScoreIsFavoritedResponse,
  UserScoreRatingCreate,
  UserScoreRatingResponse,
  ScoreRatingSummaryResponse,
  ScoreCommentCreate,
  ScoreCommentUpdate,
  ScoreCommentResponse,
  ScoreType,
  ScoreDifficulty,
  ScoreStatus
} from './model/scoreModel'
import { UserFavoriteResponse, Msg } from './model/userModel'

export class ScoreService {
  // 创建新谱曲
  static createScore(params: ScoreCreate) {
    return request.post<ScoreResponse>({
      url: '/api/v1/scores/',
      data: params
    })
  }

  // 获取谱曲列表
  static getScores(
    params: {
      skip?: number
      limit?: number
      sort_by?: string
      order?: 'asc' | 'desc'
      category_id?: number
      uploader_id?: number
      title?: string
      score_type?: ScoreType
      difficulty?: ScoreDifficulty
      status?: ScoreStatus
      is_premium_only?: boolean
      tags?: string
    } = {}
  ) {
    return request.get<ScorePaginatedResponse>({
      url: '/api/v1/scores/',
      params
    })
  }

  // 获取特定谱曲详情
  static getScoreDetails(scoreId: number) {
    return request.get<ScoreDetailResponse>({
      url: `/api/v1/scores/${scoreId}`
    })
  }

  // 更新谱曲信息
  static updateScore(scoreId: number, params: ScoreUpdate) {
    return request.put<ScoreResponse>({
      url: `/api/v1/scores/${scoreId}`,
      data: params
    })
  }

  // 删除谱曲
  static deleteScore(scoreId: number) {
    return request.del<Msg>({
      url: `/api/v1/scores/${scoreId}`
    })
  }

  // 收藏指定谱曲
  static favoriteScore(scoreId: number) {
    return request.post<UserFavoriteResponse>({
      url: `/api/v1/scores/${scoreId}/favorite`
    })
  }

  // 取消收藏指定谱曲
  static unfavoriteScore(scoreId: number) {
    return request.del<Msg>({
      url: `/api/v1/scores/${scoreId}/favorite`
    })
  }

  // 检查谱曲是否已收藏
  static checkScoreIsFavorited(scoreId: number) {
    return request.get<ScoreIsFavoritedResponse>({
      url: `/api/v1/scores/${scoreId}/is_favorited`
    })
  }

  // 提交或更新谱曲评分
  static rateScore(scoreId: number, params: UserScoreRatingCreate) {
    return request.post<UserScoreRatingResponse>({
      url: `/api/v1/scores/${scoreId}/rate`,
      data: params
    })
  }

  // 获取谱曲的平均评分和评分总数
  static getScoreRatingsSummary(scoreId: number) {
    return request.get<ScoreRatingSummaryResponse>({
      url: `/api/v1/scores/${scoreId}/ratings_summary`
    })
  }

  // 发表谱曲评论
  static createScoreComment(scoreId: number, params: ScoreCommentCreate) {
    return request.post<ScoreCommentResponse>({
      url: `/api/v1/scores/${scoreId}/comments/`,
      data: params
    })
  }

  // 获取谱曲的评论列表
  static getScoreComments(
    scoreId: number,
    params: {
      skip?: number
      limit?: number
      parent_id?: number
    } = {}
  ) {
    return request.get<ScoreCommentResponse[]>({
      url: `/api/v1/scores/${scoreId}/comments/`,
      params
    })
  }

  // 更新谱曲评论
  static updateScoreComment(scoreId: number, commentId: number, params: ScoreCommentUpdate) {
    return request.put<ScoreCommentResponse>({
      url: `/api/v1/scores/${scoreId}/comments/${commentId}`,
      data: params
    })
  }

  // 删除谱曲评论
  static deleteScoreComment(scoreId: number, commentId: number) {
    return request.del<Msg>({
      url: `/api/v1/scores/${scoreId}/comments/${commentId}`
    })
  }
}

export class LeaderboardService {
  // 获取热门谱曲排行榜
  static getPopularScores(
    params: {
      limit?: number
      time_range?: 'daily' | 'weekly' | 'monthly' | 'all_time'
    } = {}
  ) {
    return request.get<ScoreResponse[]>({
      url: '/api/v1/leaderboards/scores/popular',
      params
    })
  }

  // 获取最新谱曲排行榜
  static getLatestScores(params: { limit?: number } = {}) {
    return request.get<ScoreResponse[]>({
      url: '/api/v1/leaderboards/scores/latest',
      params
    })
  }

  // 获取高分谱曲排行榜
  static getTopRatedScores(
    params: {
      limit?: number
      min_ratings_count?: number
    } = {}
  ) {
    return request.get<ScoreResponse[]>({
      url: '/api/v1/leaderboards/scores/top_rated',
      params
    })
  }

  // 获取下载最多谱曲排行榜
  static getMostDownloadedScores(
    params: {
      limit?: number
      time_range?: 'daily' | 'weekly' | 'monthly' | 'all_time'
    } = {}
  ) {
    return request.get<ScoreResponse[]>({
      url: '/api/v1/leaderboards/scores/most_downloaded',
      params
    })
  }
}
