import request from '@/utils/http'
import {
  WithdrawalRecordCreate,
  WithdrawalRecordResponse,
  CommissionRecordResponse,
  LicenseKeyResponse,
  PaginatedResponse,
  Msg
} from './model/adminModel'

// 代理商API服务
export class ResellerService {
  // 获取代理商信息
  static getResellerInfo() {
    return request.get<any>({
      url: '/api/v1/resellers/me'
    })
  }

  // 获取代理商统计信息
  static getResellerStats() {
    return request.get<any>({
      url: '/api/v1/reseller/stats'
    })
  }

  // 获取佣金记录
  static getCommissionRecords(params: {
    skip?: number
    limit?: number
    start_date?: string
    end_date?: string
    status?: string
  } = {}) {
    return request.get<PaginatedResponse<CommissionRecordResponse>>({
      url: '/api/v1/resellers/me/commission-records',
      params
    })
  }

  // 获取销售的卡密列表
  static getSoldLicenseKeys(params: {
    skip?: number
    limit?: number
    license_type_id?: number
    status?: string
    start_date?: string
    end_date?: string
  } = {}) {
    return request.get<PaginatedResponse<LicenseKeyResponse>>({
      url: '/api/v1/reseller/license-keys',
      params
    })
  }

  // 创建提现记录
  static createWithdrawalRecord(data: WithdrawalRecordCreate) {
    return request.post<WithdrawalRecordResponse>({
      url: '/api/v1/resellers/me/withdrawals',
      data
    })
  }

  // 获取提现记录列表
  static getWithdrawalRecords(params: {
    skip?: number
    limit?: number
    status?: string
    start_date?: string
    end_date?: string
  } = {}) {
    return request.get<PaginatedResponse<WithdrawalRecordResponse>>({
      url: '/api/v1/resellers/me/withdrawal-records',
      params
    })
  }

  // 获取提现记录详情
  static getWithdrawalRecord(withdrawalId: number) {
    return request.get<WithdrawalRecordResponse>({
      url: `/api/v1/reseller/withdrawals/${withdrawalId}`
    })
  }

  // 取消提现记录（仅限待处理状态）
  static cancelWithdrawalRecord(withdrawalId: number) {
    return request.del<Msg>({
      url: `/api/v1/reseller/withdrawals/${withdrawalId}`
    })
  }

  // 获取可用余额
  static getBalance() {
    return request.get<{ balance: number; frozen_balance: number }>({
      url: '/api/v1/reseller/balance'
    })
  }

  // 获取销售统计
  static getSalesStats(params: {
    start_date?: string
    end_date?: string
    group_by?: 'day' | 'week' | 'month'
  } = {}) {
    return request.get<any>({
      url: '/api/v1/reseller/sales-stats',
      params
    })
  }

  // 获取代理商级别信息
  static getResellerLevel() {
    return request.get<any>({
      url: '/api/v1/reseller/level'
    })
  }

  // 获取下级代理列表（如果支持多级代理）
  static getSubResellers(params: {
    skip?: number
    limit?: number
    search?: string
  } = {}) {
    return request.get<PaginatedResponse<any>>({
      url: '/api/v1/resellers/me/downlines',
      params
    })
  }

  // 生成推广链接
  static generateReferralLink(params: {
    license_type_id?: number
    campaign?: string
  } = {}) {
    return request.post<{ referral_link: string; referral_code: string }>({
      url: '/api/v1/reseller/referral-link',
      data: params
    })
  }

  // 获取推广统计
  static getReferralStats(params: {
    start_date?: string
    end_date?: string
  } = {}) {
    return request.get<any>({
      url: '/api/v1/reseller/referral-stats',
      params
    })
  }
}

// 代理商认证服务
export class ResellerAuthService {
  // 代理商登录
  static login(params: { username: string; password: string }) {
    return request.post<any>({
      url: '/api/v1/reseller-auth/login',
      data: params
    })
  }

  // 代理商注册申请
  static applyForReseller(params: {
    username: string
    email: string
    phone_number?: string
    company_name?: string
    business_license?: string
    description?: string
  }) {
    return request.post<Msg>({
      url: '/api/v1/reseller-auth/apply',
      data: params
    })
  }

  // 代理商密码重置
  static resetPassword(params: {
    email: string
    reset_token?: string
    verification_code?: string
    new_password: string
  }) {
    return request.post<Msg>({
      url: '/api/v1/reseller-auth/reset-password',
      data: params
    })
  }

  // 代理商信息更新
  static updateProfile(params: {
    nickname?: string
    email?: string
    phone_number?: string
    company_name?: string
    avatar_url?: string
  }) {
    return request.put<any>({
      url: '/api/v1/reseller-auth/profile',
      data: params
    })
  }

  // 代理商修改密码
  static changePassword(params: {
    current_password: string
    new_password: string
  }) {
    return request.post<Msg>({
      url: '/api/v1/reseller-auth/change-password',
      data: params
    })
  }
}

// 代理商卡密管理服务
export class ResellerLicenseService {
  // 获取可销售的卡密类型
  static getAvailableLicenseTypes() {
    return request.get<any[]>({
      url: '/api/v1/reseller/license-types'
    })
  }

  // 申请卡密
  static requestLicenseKeys(params: {
    license_type_id: number
    quantity: number
    notes?: string
  }) {
    return request.post<Msg>({
      url: '/api/v1/reseller/license-keys/request',
      data: params
    })
  }

  // 获取卡密库存
  static getLicenseKeyInventory(params: {
    license_type_id?: number
    status?: string
  } = {}) {
    return request.get<any>({
      url: '/api/v1/reseller/license-keys/inventory',
      params
    })
  }

  // 标记卡密为已售出
  static markLicenseKeySold(licenseKeyId: number, params: {
    sale_price?: number
    customer_info?: string
    notes?: string
  } = {}) {
    return request.post<Msg>({
      url: `/api/v1/reseller/license-keys/${licenseKeyId}/sold`,
      data: params
    })
  }

  // 获取卡密销售记录
  static getLicenseKeySales(params: {
    skip?: number
    limit?: number
    license_type_id?: number
    start_date?: string
    end_date?: string
  } = {}) {
    return request.get<PaginatedResponse<any>>({
      url: '/api/v1/reseller/license-keys/sales',
      params
    })
  }
}
